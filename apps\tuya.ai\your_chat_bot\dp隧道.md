DP 通道优先级
更新时间：2024-06-25 13:48:24下载pdf

TuyaOS 框架根据设备当前连接通路的状态，自动选择合适的 DP 通道进行传输。

通道分类
通道	通道描述
LAN	App 和设备连接在同一台路由器上，三者组成一个局域网。
WAN（MQTT）	App 和设备均能够连接涂鸦 IoT 云，设备 MQTT 在线。
蓝牙	App 的蓝牙与设备的蓝牙连接成功。
通道优先级
默认通道优先级
LAN > WAN（MQTT）> 蓝牙，App 会优先选择高优先级通道与设备交互。

局域网通道：App 和设备在同一个局域网内时，会优先使用局域网通道。
WAN 通道：App 和设备不在同一个局域网内，但是设备连着涂鸦 IoT 云（设备 MQTT 在线），会使用 WAN 通道。
蓝牙通道：App 和设备不在同一个局域网内，并且设备无法连接涂鸦 IoT 云，会使用蓝牙通道。
自定义通道优先级
目前，涂鸦提供两种方式设置 DP 通道的优先级。

调用 API 接口进行设置

框架提供设置通道优先级的接口进行配置。该接口设置是针对全部 DP，即一旦设置了，所有 DP 交互的通道优先级都按照设置的运行。

在涂鸦开发者平台上进行配置

可针对单个 DP 设置，即可只对部分的 DP 交互通道优先级有效，其他的 DP 通道优先级还是按照默认的优先级。

设备选择上报通道的规则
设备上报数据时，先判断哪几个通道是连接正常的，只会在 连接正常的通道 内进行上报。

蓝牙与 LAN 和 WAN 这两个通道相 互斥。

设备在 LAN 或者 WAN 通道上报了数据，则不会在蓝牙通道上报数据。
设备在蓝牙通道上报了数据，则不会在 LAN 和 WAN 通道上报数据。
如果 LAN 和 WAN 通道都连着，设备在上报数据时两个通道都会上报。即 App 可能会收到 多个通道上报 的数据。

开发指导
关联头文件
tuya_iot_com_api.h
smart_frame.h
使用方法
使用默认通道优先级

在 设备初始化 过程中注册设备功能回调函数后，根据应用场景调用对应的 上报接口。接口内会自动处理上报通道。

通过接口设置通道优先级

由于每个双模模组上的资源不一样，并不会打开所有功能，您需要检查 tuya_iot_config.h 确认如下宏是否定义。

#define ENABLE_COMMUNICATE_PRIORITY 1
在 设备初始化 过程中注册设备功能回调函数前，调用设置通道优先级接口。之后 DP 的数据上报和下发就会按照您设置的优先级进行处理。

在涂鸦开发者平台上进行配置

您如果想要启用这个功能，请先前往 涂鸦开发者平台 创建产品，进入产品开发界面。在 功能定义 > 标准功能 > 编辑标准功能 > 特殊配置 处配置 DP 路由 选项。

如果您在 特殊配置 中找不到 DP 路由 的选项，是因为该产品方案并未绑定该功能。如果您有需求，联系您的涂鸦客户经理，或者 提交工单 申请绑定该能力。

DP 路由的选项说明：

路由类型	意义和使用场景
不设置	该 DP 按照默认的上报通道优先级处理。
蓝牙优先	该 DP 优先从蓝牙通道上报，没有蓝牙连接的时候，也可以从其他通道上报。
强制蓝牙	该 DP 仅从蓝牙通道上报，没有蓝牙连接的时候不上报。
API 说明
设置通道优先级
支持修改 DP 上报、下发通道的优先级。设置之后，通道优先级会针对所有的 DP 生效。通过接口可以修改默认的通道优先级，优先级不会保存，每次设备重新启动，都需要设置。

该接口需要在调用 设备初始化 设备功能回调函数接口前调用。

/**
 * @brief Set the reporting channel for DP
 *
 * @param[in] chan: array of channel for DP report, refer to TY_DP_REPT_CHAN_TP_T
 * @param[in] cnt: count of array member
 * @param[in] only: report DP only on the first channel
 *
 * @note This API is used for setting the reporting channel for DP. This API should be called before or during device initialization.
 *
 * @return OPRT_OK on success. Others on error, please refer to tuya_error_code.h
 */
OPERATE_RET ty_set_dp_rept_chan(IN UINT8_T chan[], UINT8_T cnt, BOOL_T only);
参考示例
设置通道优先级为：蓝牙 LE > LAN > WAN（MQTT）。

// Set the DP reporting channel priority as BLE > LAN > WAN(MQTT)
OPERATE_RET example_set_dp_report_chan(VOID)
{
    OPERATE_RET rt = OPRT_OK;

    UCHAR_T chan_para[] = {TY_DP_REPT_CHAN_BLE,TY_DP_REPT_CHAN_LAN,TY_DP_REPT_CHAN_MQTT};
    TUYA_CALL_ERR_RETURN(ty_set_dp_rept_chan(chan_para, CNTSOF(chan_para), TRUE));

    // Device init
    //...

    return rt;
}