# 模块提取快速参考卡片

## 🚀 快速开始

### 1. 分析源文件
```bash
# 查看文件结构，找到模块边界
head -200 source_file.md
grep -n "模块名称\|功能描述" source_file.md
```

### 2. 创建文件结构
```bash
# 创建必要的文件
touch include/module_name.h
touch src/module_name.c
touch src/module_name_test.c
touch MODULE_NAME_USAGE.md
```

### 3. 基本文件模板

#### 头文件模板 (include/module_name.h)
```c
#ifndef __MODULE_NAME_H__
#define __MODULE_NAME_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

// 配置常量
#define MODULE_MAX_SIZE    1024

// 枚举定义
typedef enum {
    MODULE_STATE_IDLE = 0,
    MODULE_STATE_ACTIVE
} module_state_e;

// 主要函数
OPERATE_RET module_name_init(void);
void module_name_cleanup(void);

// 测试函数
void module_name_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __MODULE_NAME_H__ */
```

#### 源文件模板 (src/module_name.c)
```c
#include "module_name.h"
#include "tkl_output.h"
#include <string.h>

// 内部状态结构
typedef struct {
    bool initialized;
    module_state_e state;
} module_manager_t;

static module_manager_t g_module = {0};

OPERATE_RET module_name_init(void)
{
    PR_INFO("🔧 初始化%s模块...", "MODULE_NAME");
    
    memset(&g_module, 0, sizeof(module_manager_t));
    g_module.initialized = true;
    g_module.state = MODULE_STATE_IDLE;
    
    PR_INFO("✅ %s模块初始化完成", "MODULE_NAME");
    return OPRT_OK;
}

void module_name_cleanup(void)
{
    if (!g_module.initialized) return;
    
    PR_INFO("🧹 清理%s模块...", "MODULE_NAME");
    memset(&g_module, 0, sizeof(module_manager_t));
    PR_INFO("✅ %s模块清理完成", "MODULE_NAME");
}
```

#### 测试文件模板 (src/module_name_test.c)
```c
#include "module_name.h"
#include "tkl_output.h"

void module_name_run_tests(void)
{
    PR_INFO("🧪 开始%s模块测试", "MODULE_NAME");
    
    // 测试初始化
    OPERATE_RET ret = module_name_init();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 初始化测试通过");
    } else {
        PR_ERR("❌ 初始化测试失败: %d", ret);
    }
    
    // 测试清理
    module_name_cleanup();
    PR_INFO("✅ 清理测试通过");
    
    PR_INFO("✅ %s模块测试完成", "MODULE_NAME");
}
```

## 📋 检查清单

### ✅ 文件创建
- [ ] 头文件 (include/module_name.h)
- [ ] 源文件 (src/module_name.c)
- [ ] 测试文件 (src/module_name_test.c)
- [ ] 使用文档 (MODULE_NAME_USAGE.md)

### ✅ 代码质量
- [ ] 函数命名统一 (module_name_function)
- [ ] 错误处理完整
- [ ] 日志输出适当 (PR_INFO/PR_ERR)
- [ ] 内存管理安全

### ✅ 功能完整
- [ ] 初始化函数
- [ ] 清理函数
- [ ] 主要功能函数
- [ ] 测试函数

### ✅ 文档完整
- [ ] API注释
- [ ] 使用示例
- [ ] 配置说明
- [ ] 注意事项

## 🔧 常用命令

### 查找模块边界
```bash
# 搜索模块开始标记
grep -n "模块\|管理器\|系统" source_file.md

# 查看特定行范围
sed -n '100,200p' source_file.md

# 统计代码行数
wc -l source_file.md
```

### 代码提取
```bash
# 提取特定行范围
sed -n '117,151p' 1223.md > extracted_code.c

# 查找函数定义
grep -n "static.*function_name" source_file.md

# 查找结构体定义
grep -n "typedef struct" source_file.md
```

### 编译验证
```bash
# 清理重编译
make clean && make

# 检查警告
make 2>&1 | grep warning

# 运行测试
./program --test-module
```

## 🎯 命名规范

### 文件命名
```
头文件: module_name_manager.h
源文件: module_name_manager.c
测试文件: module_name_test.c
文档文件: MODULE_NAME_USAGE.md
```

### 函数命名
```c
// 公共API
module_name_manager_init()
module_name_manager_send_data()
module_name_manager_get_status()

// 内部函数
static void internal_function_name()
```

### 变量命名
```c
// 全局变量
static module_manager_t g_module_manager;

// 结构体类型
typedef struct {
    // 字段
} module_data_t;

// 枚举类型
typedef enum {
    MODULE_STATE_INIT = 0
} module_state_e;
```

## ⚡ 快速替换

### 批量替换模板
```bash
# 替换模块名称
sed -i 's/MODULE_NAME/iot_comm_manager/g' *.h *.c

# 替换函数前缀
sed -i 's/module_name/iot_comm_manager/g' *.h *.c

# 替换宏定义
sed -i 's/__MODULE_NAME_H__/__IOT_COMM_MANAGER_H__/g' *.h
```

### 常用正则表达式
```bash
# 查找函数定义
grep -E "^(static\s+)?[A-Z_]+\s+\w+\(" source.c

# 查找结构体
grep -E "typedef\s+struct" source.c

# 查找宏定义
grep -E "^#define\s+\w+" source.c
```

## 🚨 常见错误

### 编译错误
```
错误: 'function_name' undeclared
解决: 检查头文件包含和函数声明

错误: multiple definition of 'variable'
解决: 检查全局变量定义，使用static限制作用域

错误: 'header.h' file not found
解决: 检查CMakeLists.txt中的include路径
```

### 运行时错误
```
段错误: 空指针访问
解决: 添加参数有效性检查

内存泄漏: 未释放分配的内存
解决: 确保每个malloc对应free

初始化失败: 模块未正确初始化
解决: 检查初始化顺序和返回值
```

## 📞 快速联系

## 📚 已提取模块参考

### IoT通信管理模块
```c
#include "iot_comm_manager.h"

// 初始化
iot_comm_manager_init();

// 发送数据
iot_comm_manager_send_dp_data(json_data, IOT_PRIORITY_NORMAL);

// 测试
iot_comm_manager_run_tests();
```

### GPIO管理模块
```c
#include "gpio_manager.h"

// 初始化
gpio_manager_init();

// LED控制
gpio_manager_set_led_state(LED_STATE_SLOW_BLINK);

// 按键回调
gpio_manager_register_button_callback(my_callback);

// 主循环更新
gpio_manager_update();

// 测试
gpio_manager_run_tests();
```

## 📡 传输层协议提醒

### MQTT协议要点
```c
// QoS级别选择
dev_report_dp_json_async();     // QoS 0 - 快速上报
dev_report_dp_stat_sync();      // QoS 1 - 可靠上报

// DP118上传示例
TY_OBJ_DP_S dp_data = {
    .dpid = 118, .type = PROP_STR,
    .value.dp_str = json_data, .time_stamp = 0
};
dev_report_dp_stat_sync(NULL, &dp_data, 1, 5);
```

### 网络优化策略
```c
// 根据网络质量调整
NETWORK_POOR     → QoS 0, 减少频率
NETWORK_GOOD     → QoS 1, 正常频率
NETWORK_EXCELLENT → QoS 1, 提高频率
```

## 📞 快速联系

如需帮助，请参考：
- 详细指南: MODULE_EXTRACTION_GUIDE.md
- IoT通信模块: IOT_COMM_MANAGER_USAGE.md
- GPIO管理模块: GPIO_MANAGER_USAGE.md
- 服药日志模块: MEDICATION_LOG_MANAGER_USAGE.md
- 传输层协议: MQTT_TRANSPORT_LAYER_REMINDER.md
- 测试说明: 运行对应的 *_run_tests() 函数

---
*最后更新: 2024-12-23*
