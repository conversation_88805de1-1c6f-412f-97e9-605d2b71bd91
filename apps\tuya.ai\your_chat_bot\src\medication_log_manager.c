/**
 * @file medication_log_manager.c
 * @brief 服药日志管理模块实现
 * 
 * 提供服药日志记录、Flash存储和DP118上传功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "medication_log_manager.h"
#include "iot_comm_manager.h"
#include "tkl_flash.h"
#include "tkl_output.h"
#include "tuya_iot_dp.h"
#include "cJSON.h"
#include <string.h>
#include <time.h>

// ========== 内部结构体定义 ==========

/**
 * @brief 服药日志管理器内部状态结构体
 */
typedef struct {
    bool initialized;                   /**< 初始化状态 */
    flash_log_header_t header;          /**< Flash头部信息 */
    uint32_t flash_addr;                /**< Flash地址 */
    uint32_t flash_size;                /**< Flash大小 */
    bool auto_upload_enabled;           /**< 自动上传开关 */
    upload_status_e upload_status;      /**< 上传状态 */
    uint32_t last_upload_time;          /**< 最后上传时间 */
    uint32_t upload_success_count;      /**< 上传成功次数 */
    uint32_t upload_failed_count;       /**< 上传失败次数 */
} medication_log_manager_t;

// ========== 全局变量 ==========

/**
 * @brief 全局服药日志管理器实例
 */
static medication_log_manager_t g_med_log_manager = {0};

// ========== 内部函数声明 ==========

/**
 * @brief 计算CRC32校验值
 * 
 * @param data 数据指针
 * @param len 数据长度
 * @return uint32_t CRC32值
 */
static uint32_t calculate_crc32(const void *data, size_t len);

/**
 * @brief 格式化服药记录为JSON字符串
 * 
 * @param entries 日志条目数组
 * @param count 条目数量
 * @param json_buffer JSON缓冲区
 * @param buffer_size 缓冲区大小
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET format_records_to_json(flash_log_entry_t *entries, 
                                          int count, 
                                          char *json_buffer, 
                                          size_t buffer_size);

/**
 * @brief 通过DP118上传记录
 * 
 * @param json_data JSON格式的记录数据
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET upload_to_dp118(const char *json_data);

/**
 * @brief 标记记录为已上传
 * 
 * @param start_index 开始索引
 * @param count 记录数量
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET mark_records_as_uploaded(uint32_t start_index, int count);

/**
 * @brief 获取未上传的记录
 * 
 * @param entries 记录缓冲区
 * @param max_count 最大记录数
 * @param actual_count 实际获取数量
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET get_unuploaded_records(flash_log_entry_t *entries, 
                                         int max_count, 
                                         int *actual_count);

// ========== 公共函数实现 ==========

OPERATE_RET medication_log_manager_init(void)
{
    PR_INFO("🔧 初始化服药日志管理器 (Flash存储 + DP118上传)...");
    
    // 清空管理器状态
    memset(&g_med_log_manager, 0, sizeof(medication_log_manager_t));
    
    // 设置Flash配置
    g_med_log_manager.flash_addr = MEDICATION_LOG_FLASH_ADDR;
    g_med_log_manager.flash_size = MEDICATION_LOG_FLASH_SIZE;
    g_med_log_manager.auto_upload_enabled = true;
    g_med_log_manager.upload_status = UPLOAD_STATUS_IDLE;
    
    // 初始化Flash驱动
    OPERATE_RET ret = tkl_flash_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ Flash驱动初始化失败: %d", ret);
        return ret;
    }
    
    // 读取Flash头部
    ret = tkl_flash_read(g_med_log_manager.flash_addr, 
                        (uint8_t*)&g_med_log_manager.header, 
                        sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 读取Flash头部失败: %d", ret);
        return ret;
    }
    
    // 检查魔数，如果不匹配则初始化Flash区域
    if (g_med_log_manager.header.magic != MEDICATION_LOG_MAGIC) {
        PR_INFO("🔧 首次使用，初始化Flash日志区域...");
        
        // 擦除Flash区域
        ret = tkl_flash_erase(g_med_log_manager.flash_addr, g_med_log_manager.flash_size);
        if (ret != OPRT_OK) {
            PR_ERR("❌ Flash擦除失败: %d", ret);
            return ret;
        }
        
        // 初始化头部
        memset(&g_med_log_manager.header, 0, sizeof(flash_log_header_t));
        g_med_log_manager.header.magic = MEDICATION_LOG_MAGIC;
        g_med_log_manager.header.version = 1;
        g_med_log_manager.header.entry_count = 0;
        g_med_log_manager.header.write_index = 0;
        g_med_log_manager.header.last_upload_index = 0;
        
        // 写入头部
        ret = tkl_flash_write(g_med_log_manager.flash_addr, 
                             (uint8_t*)&g_med_log_manager.header, 
                             sizeof(flash_log_header_t));
        if (ret != OPRT_OK) {
            PR_ERR("❌ 写入Flash头部失败: %d", ret);
            return ret;
        }
    }
    
    g_med_log_manager.initialized = true;
    
    PR_INFO("✅ 服药日志管理器初始化完成");
    PR_INFO("📊 配置信息:");
    PR_INFO("   - Flash地址: 0x%X", g_med_log_manager.flash_addr);
    PR_INFO("   - 存储空间: %d KB", g_med_log_manager.flash_size / 1024);
    PR_INFO("   - 已有记录: %d条", g_med_log_manager.header.entry_count);
    PR_INFO("   - 自动上传: %s", g_med_log_manager.auto_upload_enabled ? "启用" : "禁用");
    PR_INFO("   - 上传DP点: DP%d (服药记录缓存)", DP_MEDICATION_RECORD);
    
    // 显示初始化成功消息
    medication_log_manager_display_status("💊 服药日志系统已启动 (Flash+DP118)");
    
    return OPRT_OK;
}

void medication_log_manager_cleanup(void)
{
    if (!g_med_log_manager.initialized) {
        PR_WARN("⚠️ 服药日志管理器未初始化");
        return;
    }
    
    PR_INFO("🧹 清理服药日志管理器...");
    
    // 如果有待上传的记录，尝试最后一次上传
    if (g_med_log_manager.upload_status == UPLOAD_STATUS_PENDING) {
        PR_INFO("📤 清理前尝试上传待处理记录...");
        medication_log_manager_upload_records();
    }
    
    // 清空状态
    memset(&g_med_log_manager, 0, sizeof(medication_log_manager_t));
    
    PR_INFO("✅ 服药日志管理器清理完成");
}

OPERATE_RET medication_log_manager_write_dispense(const char *medication_name)
{
    if (!g_med_log_manager.initialized) {
        PR_ERR("❌ 服药日志管理器未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (!medication_name) {
        PR_ERR("❌ 药品名称为空");
        return OPRT_INVALID_PARM;
    }
    
    PR_INFO("📝 记录服药日志: %s", medication_name);
    
    // 创建日志条目
    flash_log_entry_t entry = {0};
    entry.magic = MEDICATION_LOG_MAGIC;
    entry.timestamp = tal_time_get_posix();
    entry.uploaded = false;
    strncpy(entry.medication_name, medication_name, sizeof(entry.medication_name) - 1);
    
    // 格式化日志内容
    TIME_T current_time = tal_time_get_posix();
    time_t std_time = (time_t)current_time;
    struct tm *time_info = localtime(&std_time);
    
    if (time_info) {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "%04d-%02d-%02d %02d:%02d 服用 %s",
                time_info->tm_year + 1900, time_info->tm_mon + 1, time_info->tm_mday,
                time_info->tm_hour, time_info->tm_min, medication_name);
    } else {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "时间戳%ld 服用 %s", current_time, medication_name);
    }
    
    // 计算CRC
    entry.crc = calculate_crc32(&entry, sizeof(entry) - sizeof(entry.crc));
    
    // 计算Flash写入地址
    uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                         (g_med_log_manager.header.write_index * sizeof(flash_log_entry_t));
    
    // 写入Flash
    OPERATE_RET ret = tkl_flash_write(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 写入Flash日志失败: %d", ret);
        return ret;
    }
    
    // 更新头部信息
    g_med_log_manager.header.write_index = (g_med_log_manager.header.write_index + 1) % MAX_LOG_ENTRIES_FLASH;
    if (g_med_log_manager.header.entry_count < MAX_LOG_ENTRIES_FLASH) {
        g_med_log_manager.header.entry_count++;
    }
    
    // 写回头部
    ret = tkl_flash_write(g_med_log_manager.flash_addr, 
                         (uint8_t*)&g_med_log_manager.header, 
                         sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 更新Flash头部失败: %d", ret);
        return ret;
    }
    
    PR_INFO("✅ 服药日志已写入Flash: %s (总记录: %d)", 
            medication_name, g_med_log_manager.header.entry_count);
    
    // 显示服药成功消息
    char display_msg[128];
    snprintf(display_msg, sizeof(display_msg), "✅ %s 服药完成 (已记录)", medication_name);
    medication_log_manager_display_success(display_msg);
    
    // 如果启用自动上传，设置待上传状态
    if (g_med_log_manager.auto_upload_enabled) {
        g_med_log_manager.upload_status = UPLOAD_STATUS_PENDING;
        PR_DEBUG("📤 已设置为待上传状态");
    }
    
    return OPRT_OK;
}
