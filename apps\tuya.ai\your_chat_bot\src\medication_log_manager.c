/**
 * @file medication_log_manager.c
 * @brief 服药日志管理模块实现
 * 
 * 提供服药日志记录、Flash存储和DP118上传功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "medication_log_manager.h"
#include "iot_comm_manager.h"
#include "tkl_flash.h"
#include "tkl_output.h"
#include "tuya_iot_dp.h"
#include "cJSON.h"
#include <string.h>
#include <time.h>

// ========== 内部结构体定义 ==========

/**
 * @brief 服药日志管理器内部状态结构体
 */
typedef struct {
    bool initialized;                   /**< 初始化状态 */
    flash_log_header_t header;          /**< Flash头部信息 */
    uint32_t flash_addr;                /**< Flash地址 */
    uint32_t flash_size;                /**< Flash大小 */
    bool auto_upload_enabled;           /**< 自动上传开关 */
    upload_status_e upload_status;      /**< 上传状态 */
    uint32_t last_upload_time;          /**< 最后上传时间 */
    uint32_t upload_success_count;      /**< 上传成功次数 */
    uint32_t upload_failed_count;       /**< 上传失败次数 */
} medication_log_manager_t;

// ========== 全局变量 ==========

/**
 * @brief 全局服药日志管理器实例
 */
static medication_log_manager_t g_med_log_manager = {0};

// ========== 内部函数声明 ==========

/**
 * @brief 计算CRC32校验值
 * 
 * @param data 数据指针
 * @param len 数据长度
 * @return uint32_t CRC32值
 */
static uint32_t calculate_crc32(const void *data, size_t len);

/**
 * @brief 格式化服药记录为JSON字符串
 * 
 * @param entries 日志条目数组
 * @param count 条目数量
 * @param json_buffer JSON缓冲区
 * @param buffer_size 缓冲区大小
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET format_records_to_json(flash_log_entry_t *entries, 
                                          int count, 
                                          char *json_buffer, 
                                          size_t buffer_size);

/**
 * @brief 通过DP118上传记录
 * 
 * @param json_data JSON格式的记录数据
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET upload_to_dp118(const char *json_data);

/**
 * @brief 标记记录为已上传
 * 
 * @param start_index 开始索引
 * @param count 记录数量
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET mark_records_as_uploaded(uint32_t start_index, int count);

/**
 * @brief 获取未上传的记录
 * 
 * @param entries 记录缓冲区
 * @param max_count 最大记录数
 * @param actual_count 实际获取数量
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET get_unuploaded_records(flash_log_entry_t *entries, 
                                         int max_count, 
                                         int *actual_count);

// ========== 公共函数实现 ==========

OPERATE_RET medication_log_manager_init(void)
{
    PR_INFO("🔧 初始化服药日志管理器 (Flash存储 + DP118上传)...");
    
    // 清空管理器状态
    memset(&g_med_log_manager, 0, sizeof(medication_log_manager_t));
    
    // 设置Flash配置
    g_med_log_manager.flash_addr = MEDICATION_LOG_FLASH_ADDR;
    g_med_log_manager.flash_size = MEDICATION_LOG_FLASH_SIZE;
    g_med_log_manager.auto_upload_enabled = true;
    g_med_log_manager.upload_status = UPLOAD_STATUS_IDLE;
    
    // 初始化Flash驱动
    OPERATE_RET ret = tkl_flash_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ Flash驱动初始化失败: %d", ret);
        return ret;
    }
    
    // 读取Flash头部
    ret = tkl_flash_read(g_med_log_manager.flash_addr, 
                        (uint8_t*)&g_med_log_manager.header, 
                        sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 读取Flash头部失败: %d", ret);
        return ret;
    }
    
    // 检查魔数，如果不匹配则初始化Flash区域
    if (g_med_log_manager.header.magic != MEDICATION_LOG_MAGIC) {
        PR_INFO("🔧 首次使用，初始化Flash日志区域...");
        
        // 擦除Flash区域
        ret = tkl_flash_erase(g_med_log_manager.flash_addr, g_med_log_manager.flash_size);
        if (ret != OPRT_OK) {
            PR_ERR("❌ Flash擦除失败: %d", ret);
            return ret;
        }
        
        // 初始化头部
        memset(&g_med_log_manager.header, 0, sizeof(flash_log_header_t));
        g_med_log_manager.header.magic = MEDICATION_LOG_MAGIC;
        g_med_log_manager.header.version = 1;
        g_med_log_manager.header.entry_count = 0;
        g_med_log_manager.header.write_index = 0;
        g_med_log_manager.header.last_upload_index = 0;
        
        // 写入头部
        ret = tkl_flash_write(g_med_log_manager.flash_addr, 
                             (uint8_t*)&g_med_log_manager.header, 
                             sizeof(flash_log_header_t));
        if (ret != OPRT_OK) {
            PR_ERR("❌ 写入Flash头部失败: %d", ret);
            return ret;
        }
    }
    
    g_med_log_manager.initialized = true;
    
    PR_INFO("✅ 服药日志管理器初始化完成");
    PR_INFO("📊 配置信息:");
    PR_INFO("   - Flash地址: 0x%X", g_med_log_manager.flash_addr);
    PR_INFO("   - 存储空间: %d KB", g_med_log_manager.flash_size / 1024);
    PR_INFO("   - 已有记录: %d条", g_med_log_manager.header.entry_count);
    PR_INFO("   - 自动上传: %s", g_med_log_manager.auto_upload_enabled ? "启用" : "禁用");
    PR_INFO("   - 上传DP点: DP%d (服药记录缓存)", DP_MEDICATION_RECORD);
    
    // 显示初始化成功消息
    medication_log_manager_display_status("💊 服药日志系统已启动 (Flash+DP118)");
    
    return OPRT_OK;
}

OPERATE_RET medication_log_manager_upload_records(void)
{
    if (!g_med_log_manager.initialized) {
        PR_ERR("❌ 服药日志管理器未初始化");
        return OPRT_COM_ERROR;
    }

    PR_INFO("📤 开始上传服药记录到DP118...");

    // 获取未上传的记录
    flash_log_entry_t unuploaded_entries[RECORD_BATCH_SIZE];
    int actual_count = 0;

    OPERATE_RET ret = get_unuploaded_records(unuploaded_entries, RECORD_BATCH_SIZE, &actual_count);
    if (ret != OPRT_OK || actual_count == 0) {
        PR_INFO("📝 没有待上传的记录");
        return OPRT_OK;
    }

    // 格式化为JSON字符串
    char json_buffer[RECORD_UPLOAD_MAX_LENGTH];
    ret = format_records_to_json(unuploaded_entries, actual_count, json_buffer, sizeof(json_buffer));
    if (ret != OPRT_OK) {
        PR_ERR("❌ JSON格式化失败: %d", ret);
        return ret;
    }

    // 通过DP118上传
    ret = upload_to_dp118(json_buffer);
    if (ret == OPRT_OK) {
        // 标记为已上传
        mark_records_as_uploaded(0, actual_count);
        g_med_log_manager.upload_success_count++;
        g_med_log_manager.upload_status = UPLOAD_STATUS_SUCCESS;

        PR_INFO("✅ 成功上传%d条服药记录到DP118", actual_count);
        medication_log_manager_display_success("📤 服药记录已同步到云端");
    } else {
        g_med_log_manager.upload_failed_count++;
        g_med_log_manager.upload_status = UPLOAD_STATUS_FAILED;
        PR_ERR("❌ DP118上传失败: %d", ret);
    }

    return ret;
}

OPERATE_RET medication_log_manager_get_stats(medication_log_stats_t *stats)
{
    if (!stats) {
        return OPRT_INVALID_PARM;
    }

    if (!g_med_log_manager.initialized) {
        memset(stats, 0, sizeof(medication_log_stats_t));
        return OPRT_COM_ERROR;
    }

    stats->total_records = g_med_log_manager.header.entry_count;
    stats->uploaded_records = 0;
    stats->pending_records = 0;

    // 统计已上传和待上传记录数
    for (uint32_t i = 0; i < g_med_log_manager.header.entry_count; i++) {
        flash_log_entry_t entry;
        uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                             (i * sizeof(flash_log_entry_t));

        OPERATE_RET ret = tkl_flash_read(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        if (ret == OPRT_OK && entry.magic == MEDICATION_LOG_MAGIC) {
            if (entry.uploaded) {
                stats->uploaded_records++;
            } else {
                stats->pending_records++;
            }
        }
    }

    stats->upload_success_count = g_med_log_manager.upload_success_count;
    stats->upload_failed_count = g_med_log_manager.upload_failed_count;
    stats->current_status = g_med_log_manager.upload_status;

    return OPRT_OK;
}

OPERATE_RET medication_log_manager_get_recent_records(char records[][128], int max_records, int *actual_count)
{
    if (!records || !actual_count || max_records <= 0) {
        return OPRT_INVALID_PARM;
    }

    if (!g_med_log_manager.initialized) {
        *actual_count = 0;
        return OPRT_COM_ERROR;
    }

    *actual_count = 0;
    int records_to_read = (g_med_log_manager.header.entry_count < max_records) ?
                         g_med_log_manager.header.entry_count : max_records;

    // 从最新记录开始读取
    for (int i = 0; i < records_to_read; i++) {
        uint32_t index = (g_med_log_manager.header.write_index - 1 - i + MAX_LOG_ENTRIES_FLASH) % MAX_LOG_ENTRIES_FLASH;
        uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                             (index * sizeof(flash_log_entry_t));

        flash_log_entry_t entry;
        OPERATE_RET ret = tkl_flash_read(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        if (ret == OPRT_OK && entry.magic == MEDICATION_LOG_MAGIC) {
            // 验证CRC
            uint32_t calculated_crc = calculate_crc32(&entry, sizeof(entry) - sizeof(entry.crc));
            if (calculated_crc == entry.crc) {
                strncpy(records[*actual_count], entry.log_entry, 127);
                records[*actual_count][127] = '\0';
                (*actual_count)++;
            }
        }
    }

    return OPRT_OK;
}

OPERATE_RET medication_log_manager_force_upload_all(void)
{
    if (!g_med_log_manager.initialized) {
        PR_ERR("❌ 服药日志管理器未初始化");
        return OPRT_COM_ERROR;
    }

    PR_INFO("🔄 强制上传所有服药记录...");

    // 将所有记录标记为未上传
    for (uint32_t i = 0; i < g_med_log_manager.header.entry_count; i++) {
        uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                             (i * sizeof(flash_log_entry_t));

        flash_log_entry_t entry;
        OPERATE_RET ret = tkl_flash_read(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        if (ret == OPRT_OK && entry.magic == MEDICATION_LOG_MAGIC) {
            entry.uploaded = false;
            tkl_flash_write(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        }
    }

    // 执行上传
    return medication_log_manager_upload_records();
}

OPERATE_RET medication_log_manager_clear_all_records(void)
{
    if (!g_med_log_manager.initialized) {
        PR_ERR("❌ 服药日志管理器未初始化");
        return OPRT_COM_ERROR;
    }

    PR_WARN("⚠️ 清空所有服药记录 (谨慎操作)");

    // 擦除Flash区域
    OPERATE_RET ret = tkl_flash_erase(g_med_log_manager.flash_addr, g_med_log_manager.flash_size);
    if (ret != OPRT_OK) {
        PR_ERR("❌ Flash擦除失败: %d", ret);
        return ret;
    }

    // 重新初始化头部
    memset(&g_med_log_manager.header, 0, sizeof(flash_log_header_t));
    g_med_log_manager.header.magic = MEDICATION_LOG_MAGIC;
    g_med_log_manager.header.version = 1;
    g_med_log_manager.header.entry_count = 0;
    g_med_log_manager.header.write_index = 0;
    g_med_log_manager.header.last_upload_index = 0;

    // 写入头部
    ret = tkl_flash_write(g_med_log_manager.flash_addr,
                         (uint8_t*)&g_med_log_manager.header,
                         sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 写入Flash头部失败: %d", ret);
        return ret;
    }

    PR_INFO("✅ 所有服药记录已清空");
    return OPRT_OK;
}

void medication_log_manager_set_auto_upload(bool auto_upload)
{
    if (!g_med_log_manager.initialized) {
        PR_WARN("⚠️ 服药日志管理器未初始化");
        return;
    }

    g_med_log_manager.auto_upload_enabled = auto_upload;
    PR_INFO("📤 自动上传模式: %s", auto_upload ? "启用" : "禁用");
}

void medication_log_manager_update(void)
{
    if (!g_med_log_manager.initialized || !g_med_log_manager.auto_upload_enabled) {
        return;
    }

    // 检查是否有待上传的记录
    if (g_med_log_manager.upload_status == UPLOAD_STATUS_PENDING) {
        uint32_t current_time = tal_system_get_millisecond();

        // 每60秒尝试一次自动上传
        if (current_time - g_med_log_manager.last_upload_time >= 60000) {
            g_med_log_manager.last_upload_time = current_time;
            g_med_log_manager.upload_status = UPLOAD_STATUS_UPLOADING;

            OPERATE_RET ret = medication_log_manager_upload_records();
            if (ret != OPRT_OK) {
                g_med_log_manager.upload_status = UPLOAD_STATUS_PENDING; // 重试
            }
        }
    }
}

// ========== 显示函数实现 ==========

void medication_log_manager_display_status(const char *status_msg)
{
    if (!status_msg) return;

    PR_INFO("📊 %s", status_msg);

    // 通过DP108发送状态信息到前端
    char dp_json[256];
    snprintf(dp_json, sizeof(dp_json), "{\"%d\":\"%s\"}", DP_TEST_LOG, status_msg);

    // 调用IoT通信管理器发送
    if (iot_comm_manager_send_dp_data) {
        iot_comm_manager_send_dp_data(dp_json, IOT_PRIORITY_NORMAL);
    }
}

void medication_log_manager_display_success(const char *success_msg)
{
    if (!success_msg) return;

    PR_INFO("✅ %s", success_msg);

    // 通过DP108发送成功信息到前端
    char dp_json[256];
    snprintf(dp_json, sizeof(dp_json), "{\"%d\":\"✅ %s\"}", DP_TEST_LOG, success_msg);

    // 调用IoT通信管理器发送
    if (iot_comm_manager_send_dp_data) {
        iot_comm_manager_send_dp_data(dp_json, IOT_PRIORITY_HIGH);
    }
}

void medication_log_manager_print_recent(void)
{
    if (!g_med_log_manager.initialized) {
        PR_WARN("⚠️ 服药日志管理器未初始化");
        return;
    }

    char recent_records[10][128];
    int actual_count = 0;

    OPERATE_RET ret = medication_log_manager_get_recent_records(recent_records, 10, &actual_count);
    if (ret == OPRT_OK && actual_count > 0) {
        PR_INFO("📋 最近%d条服药记录:", actual_count);
        for (int i = 0; i < actual_count; i++) {
            PR_INFO("  %d. %s", i + 1, recent_records[i]);
        }
    } else {
        PR_INFO("📋 暂无服药记录");
    }
}

// ========== 内部函数实现 ==========

static uint32_t calculate_crc32(const void *data, size_t len)
{
    const uint8_t *bytes = (const uint8_t *)data;
    uint32_t crc = 0xFFFFFFFF;

    for (size_t i = 0; i < len; i++) {
        crc ^= bytes[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }

    return ~crc;
}

static OPERATE_RET format_records_to_json(flash_log_entry_t *entries, int count, char *json_buffer, size_t buffer_size)
{
    if (!entries || !json_buffer || count <= 0 || buffer_size < 50) {
        return OPRT_INVALID_PARM;
    }

    // 开始构建JSON数组
    strcpy(json_buffer, "[");
    size_t current_len = 1;

    for (int i = 0; i < count; i++) {
        char record_json[200];

        // 格式化单条记录为JSON
        snprintf(record_json, sizeof(record_json),
                "%s{\"timestamp\":%u,\"medication\":\"%s\",\"log\":\"%s\",\"uploaded\":%s}",
                (i > 0) ? "," : "",
                entries[i].timestamp,
                entries[i].medication_name,
                entries[i].log_entry,
                entries[i].uploaded ? "true" : "false");

        // 检查缓冲区空间
        if (current_len + strlen(record_json) + 2 >= buffer_size) {
            PR_WARN("⚠️ JSON缓冲区空间不足，截断记录");
            break;
        }

        strcat(json_buffer, record_json);
        current_len += strlen(record_json);
    }

    strcat(json_buffer, "]");

    PR_DEBUG("📄 格式化JSON: %s", json_buffer);
    return OPRT_OK;
}

static OPERATE_RET upload_to_dp118(const char *json_data)
{
    if (!json_data) {
        return OPRT_INVALID_PARM;
    }

    PR_DEBUG("📤 通过DP118上传数据: %s", json_data);

    // 构建DP数据结构
    TY_OBJ_DP_S dp_data;
    dp_data.dpid = DP_MEDICATION_RECORD;  // DP118
    dp_data.type = PROP_STR;              // 字符串类型
    dp_data.value.dp_str = (char*)json_data;
    dp_data.time_stamp = 0;               // 实时型DP，时间戳填0

    // 调用涂鸦IoT同步上报接口
    OPERATE_RET ret = dev_report_dp_stat_sync(NULL, &dp_data, 1, 5);

    if (ret == OPRT_OK) {
        PR_INFO("✅ DP118上传成功");
    } else {
        PR_ERR("❌ DP118上传失败: %d", ret);
    }

    return ret;
}

static OPERATE_RET mark_records_as_uploaded(uint32_t start_index, int count)
{
    if (!g_med_log_manager.initialized || count <= 0) {
        return OPRT_INVALID_PARM;
    }

    for (int i = 0; i < count; i++) {
        uint32_t index = (start_index + i) % MAX_LOG_ENTRIES_FLASH;
        uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                             (index * sizeof(flash_log_entry_t));

        flash_log_entry_t entry;
        OPERATE_RET ret = tkl_flash_read(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        if (ret == OPRT_OK && entry.magic == MEDICATION_LOG_MAGIC) {
            entry.uploaded = true;

            // 重新计算CRC
            entry.crc = calculate_crc32(&entry, sizeof(entry) - sizeof(entry.crc));

            // 写回Flash
            ret = tkl_flash_write(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
            if (ret != OPRT_OK) {
                PR_ERR("❌ 标记记录%d为已上传失败: %d", i, ret);
                return ret;
            }
        }
    }

    // 更新头部的最后上传索引
    g_med_log_manager.header.last_upload_index = (start_index + count - 1) % MAX_LOG_ENTRIES_FLASH;

    OPERATE_RET ret = tkl_flash_write(g_med_log_manager.flash_addr,
                                     (uint8_t*)&g_med_log_manager.header,
                                     sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 更新头部信息失败: %d", ret);
        return ret;
    }

    return OPRT_OK;
}

static OPERATE_RET get_unuploaded_records(flash_log_entry_t *entries, int max_count, int *actual_count)
{
    if (!entries || !actual_count || max_count <= 0) {
        return OPRT_INVALID_PARM;
    }

    if (!g_med_log_manager.initialized) {
        *actual_count = 0;
        return OPRT_COM_ERROR;
    }

    *actual_count = 0;

    // 遍历所有记录，查找未上传的
    for (uint32_t i = 0; i < g_med_log_manager.header.entry_count && *actual_count < max_count; i++) {
        uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                             (i * sizeof(flash_log_entry_t));

        flash_log_entry_t entry;
        OPERATE_RET ret = tkl_flash_read(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
        if (ret == OPRT_OK && entry.magic == MEDICATION_LOG_MAGIC) {
            // 验证CRC
            uint32_t calculated_crc = calculate_crc32(&entry, sizeof(entry) - sizeof(entry.crc));
            if (calculated_crc == entry.crc && !entry.uploaded) {
                memcpy(&entries[*actual_count], &entry, sizeof(flash_log_entry_t));
                (*actual_count)++;
            }
        }
    }

    return OPRT_OK;
}

void medication_log_manager_cleanup(void)
{
    if (!g_med_log_manager.initialized) {
        PR_WARN("⚠️ 服药日志管理器未初始化");
        return;
    }
    
    PR_INFO("🧹 清理服药日志管理器...");
    
    // 如果有待上传的记录，尝试最后一次上传
    if (g_med_log_manager.upload_status == UPLOAD_STATUS_PENDING) {
        PR_INFO("📤 清理前尝试上传待处理记录...");
        medication_log_manager_upload_records();
    }
    
    // 清空状态
    memset(&g_med_log_manager, 0, sizeof(medication_log_manager_t));
    
    PR_INFO("✅ 服药日志管理器清理完成");
}

OPERATE_RET medication_log_manager_write_dispense(const char *medication_name)
{
    if (!g_med_log_manager.initialized) {
        PR_ERR("❌ 服药日志管理器未初始化");
        return OPRT_COM_ERROR;
    }
    
    if (!medication_name) {
        PR_ERR("❌ 药品名称为空");
        return OPRT_INVALID_PARM;
    }
    
    PR_INFO("📝 记录服药日志: %s", medication_name);
    
    // 创建日志条目
    flash_log_entry_t entry = {0};
    entry.magic = MEDICATION_LOG_MAGIC;
    entry.timestamp = tal_time_get_posix();
    entry.uploaded = false;
    strncpy(entry.medication_name, medication_name, sizeof(entry.medication_name) - 1);
    
    // 格式化日志内容
    TIME_T current_time = tal_time_get_posix();
    time_t std_time = (time_t)current_time;
    struct tm *time_info = localtime(&std_time);
    
    if (time_info) {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "%04d-%02d-%02d %02d:%02d 服用 %s",
                time_info->tm_year + 1900, time_info->tm_mon + 1, time_info->tm_mday,
                time_info->tm_hour, time_info->tm_min, medication_name);
    } else {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "时间戳%ld 服用 %s", current_time, medication_name);
    }
    
    // 计算CRC
    entry.crc = calculate_crc32(&entry, sizeof(entry) - sizeof(entry.crc));
    
    // 计算Flash写入地址
    uint32_t entry_addr = g_med_log_manager.flash_addr + sizeof(flash_log_header_t) +
                         (g_med_log_manager.header.write_index * sizeof(flash_log_entry_t));
    
    // 写入Flash
    OPERATE_RET ret = tkl_flash_write(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 写入Flash日志失败: %d", ret);
        return ret;
    }
    
    // 更新头部信息
    g_med_log_manager.header.write_index = (g_med_log_manager.header.write_index + 1) % MAX_LOG_ENTRIES_FLASH;
    if (g_med_log_manager.header.entry_count < MAX_LOG_ENTRIES_FLASH) {
        g_med_log_manager.header.entry_count++;
    }
    
    // 写回头部
    ret = tkl_flash_write(g_med_log_manager.flash_addr, 
                         (uint8_t*)&g_med_log_manager.header, 
                         sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 更新Flash头部失败: %d", ret);
        return ret;
    }
    
    PR_INFO("✅ 服药日志已写入Flash: %s (总记录: %d)", 
            medication_name, g_med_log_manager.header.entry_count);
    
    // 显示服药成功消息
    char display_msg[128];
    snprintf(display_msg, sizeof(display_msg), "✅ %s 服药完成 (已记录)", medication_name);
    medication_log_manager_display_success(display_msg);
    
    // 如果启用自动上传，设置待上传状态
    if (g_med_log_manager.auto_upload_enabled) {
        g_med_log_manager.upload_status = UPLOAD_STATUS_PENDING;
        PR_DEBUG("📤 已设置为待上传状态");
    }
    
    return OPRT_OK;
}
