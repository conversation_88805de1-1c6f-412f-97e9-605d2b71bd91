/**
 * @file gpio_manager.c
 * @brief GPIO管理模块实现
 * 
 * 提供LED控制系统、按键管理和GPIO状态更新功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "gpio_manager.h"
#include "tal_api.h"
#include "tkl_output.h"
#include <string.h>

// ========== 内部结构体定义 ==========

/**
 * @brief GPIO管理器内部状态结构体
 */
typedef struct {
    bool initialized;                       /**< 初始化状态 */
    led_state_e system_led_state;          /**< 系统LED状态 */
    button_state_e button_state;           /**< 按键状态 */
    bool led_current_state;                 /**< LED当前物理状态 */
    uint32_t last_blink_time;              /**< 最后闪烁时间 */
    uint32_t button_last_read_time;        /**< 按键最后读取时间 */
    uint32_t button_press_start_time;      /**< 按键按下开始时间 */
    bool button_last_physical_state;       /**< 按键最后物理状态 */
    button_event_callback_t button_callback; /**< 按键事件回调函数 */
    uint32_t total_button_clicks;          /**< 总按键点击次数 */
    uint32_t total_long_presses;           /**< 总长按次数 */
} gpio_manager_t;

// ========== 全局变量 ==========

/**
 * @brief 全局GPIO管理器实例
 */
static gpio_manager_t g_gpio_manager = {0};

// ========== 内部函数声明 ==========

/**
 * @brief 读取按键物理状态
 * 
 * @return bool 按键状态 (true=按下, false=释放)
 */
static bool read_button_physical_state(void);

/**
 * @brief 处理按键状态变化
 * 
 * @param current_state 当前按键物理状态
 * @param current_time 当前时间戳
 */
static void handle_button_state_change(bool current_state, uint32_t current_time);

/**
 * @brief 获取LED状态名称
 * 
 * @param state LED状态枚举值
 * @return const char* 状态名称字符串
 */
static const char* get_led_state_name(led_state_e state);

/**
 * @brief 获取按键状态名称
 * 
 * @param state 按键状态枚举值
 * @return const char* 状态名称字符串
 */
static const char* get_button_state_name(button_state_e state);

// ========== 公共函数实现 ==========

OPERATE_RET gpio_manager_init(void)
{
    PR_INFO("🔧 初始化GPIO管理器...");
    
    // 清空管理器状态
    memset(&g_gpio_manager, 0, sizeof(gpio_manager_t));
    
    // 初始化系统LED (P01)
    TUYA_GPIO_BASE_CFG_T led_cfg = {
        .mode = TUYA_GPIO_PUSH_PULL,
        .direct = TUYA_GPIO_OUTPUT,
        .level = TUYA_GPIO_LEVEL_LOW
    };
    
    OPERATE_RET ret = tal_gpio_init(GPIO_LED_USER, &led_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 系统LED初始化失败: %d", ret);
        return ret;
    }
    
    // 初始化用户按键 (P12)
    TUYA_GPIO_BASE_CFG_T button_cfg = {
        .mode = TUYA_GPIO_PULLUP,
        .direct = TUYA_GPIO_INPUT,
        .level = TUYA_GPIO_LEVEL_HIGH
    };
    
    ret = tal_gpio_init(GPIO_BUTTON_USER, &button_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 用户按键初始化失败: %d", ret);
        return ret;
    }
    
    // 设置初始状态
    g_gpio_manager.initialized = true;
    g_gpio_manager.system_led_state = LED_STATE_SLOW_BLINK;
    g_gpio_manager.button_state = BUTTON_STATE_RELEASED;
    g_gpio_manager.led_current_state = false;
    g_gpio_manager.last_blink_time = tal_system_get_millisecond();
    g_gpio_manager.button_last_read_time = tal_system_get_millisecond();
    g_gpio_manager.button_last_physical_state = true; // 上拉输入，默认高电平
    
    PR_INFO("✅ GPIO管理器初始化完成");
    PR_INFO("📊 配置信息:");
    PR_INFO("   - 系统LED: P%02d (推挽输出)", GPIO_LED_USER);
    PR_INFO("   - 用户按键: P%02d (上拉输入)", GPIO_BUTTON_USER);
    PR_INFO("   - 防抖时间: %d ms", GPIO_DEBOUNCE_TIME_MS);
    PR_INFO("   - 长按时间: %d ms", GPIO_LONG_PRESS_TIME_MS);
    
    return OPRT_OK;
}

void gpio_manager_cleanup(void)
{
    if (!g_gpio_manager.initialized) {
        PR_WARN("⚠️ GPIO管理器未初始化");
        return;
    }
    
    PR_INFO("🧹 清理GPIO管理器...");
    
    // 关闭LED
    tal_gpio_write(GPIO_LED_USER, TUYA_GPIO_LEVEL_LOW);
    
    // 清空状态
    memset(&g_gpio_manager, 0, sizeof(gpio_manager_t));
    
    PR_INFO("✅ GPIO管理器清理完成");
}

void gpio_manager_set_led_state(led_state_e state)
{
    if (!g_gpio_manager.initialized) {
        PR_WARN("⚠️ GPIO管理器未初始化");
        return;
    }
    
    if (g_gpio_manager.system_led_state == state) {
        return; // 状态未改变
    }
    
    PR_INFO("💡 设置LED状态: %s -> %s", 
            get_led_state_name(g_gpio_manager.system_led_state),
            get_led_state_name(state));
    
    g_gpio_manager.system_led_state = state;
    g_gpio_manager.last_blink_time = tal_system_get_millisecond();
    
    switch (state) {
        case LED_STATE_OFF:
            tal_gpio_write(GPIO_LED_USER, TUYA_GPIO_LEVEL_LOW);
            g_gpio_manager.led_current_state = false;
            break;
            
        case LED_STATE_ON:
            tal_gpio_write(GPIO_LED_USER, TUYA_GPIO_LEVEL_HIGH);
            g_gpio_manager.led_current_state = true;
            break;
            
        case LED_STATE_SLOW_BLINK:
        case LED_STATE_FAST_BLINK:
            // 闪烁状态在update函数中处理
            break;
    }
}

led_state_e gpio_manager_get_led_state(void)
{
    return g_gpio_manager.initialized ? g_gpio_manager.system_led_state : LED_STATE_OFF;
}

button_state_e gpio_manager_get_button_state(void)
{
    return g_gpio_manager.initialized ? g_gpio_manager.button_state : BUTTON_STATE_RELEASED;
}

void gpio_manager_register_button_callback(button_event_callback_t callback)
{
    if (!g_gpio_manager.initialized) {
        PR_WARN("⚠️ GPIO管理器未初始化");
        return;
    }
    
    g_gpio_manager.button_callback = callback;
    PR_INFO("📞 按键事件回调函数已注册: %p", callback);
}

void gpio_manager_update(void)
{
    if (!g_gpio_manager.initialized) {
        return;
    }
    
    uint32_t current_time = tal_system_get_millisecond();
    
    // 处理LED闪烁
    uint32_t blink_interval = 0;
    switch (g_gpio_manager.system_led_state) {
        case LED_STATE_SLOW_BLINK:
            blink_interval = GPIO_BLINK_SLOW_INTERVAL;
            break;
        case LED_STATE_FAST_BLINK:
            blink_interval = GPIO_BLINK_FAST_INTERVAL;
            break;
        default:
            blink_interval = 0;
            break;
    }
    
    if (blink_interval > 0 && 
        (current_time - g_gpio_manager.last_blink_time >= blink_interval)) {
        g_gpio_manager.last_blink_time = current_time;
        g_gpio_manager.led_current_state = !g_gpio_manager.led_current_state;
        
        tal_gpio_write(GPIO_LED_USER, 
                      g_gpio_manager.led_current_state ? TUYA_GPIO_LEVEL_HIGH : TUYA_GPIO_LEVEL_LOW);
    }
    
    // 处理按键检测 (每10ms检测一次)
    if (current_time - g_gpio_manager.button_last_read_time >= 10) {
        g_gpio_manager.button_last_read_time = current_time;
        
        bool current_button_state = read_button_physical_state();
        
        // 检测状态变化
        if (current_button_state != g_gpio_manager.button_last_physical_state) {
            handle_button_state_change(current_button_state, current_time);
            g_gpio_manager.button_last_physical_state = current_button_state;
        }
        
        // 检测长按
        if (g_gpio_manager.button_state == BUTTON_STATE_PRESSED &&
            current_time - g_gpio_manager.button_press_start_time >= GPIO_LONG_PRESS_TIME_MS) {
            g_gpio_manager.button_state = BUTTON_STATE_LONG_PRESSED;
            g_gpio_manager.total_long_presses++;
            
            if (g_gpio_manager.button_callback) {
                uint32_t duration = current_time - g_gpio_manager.button_press_start_time;
                g_gpio_manager.button_callback(BUTTON_EVENT_LONG_PRESS, duration);
            }
            
            PR_INFO("🔘 检测到长按事件 (持续时间: %d ms)",
                    current_time - g_gpio_manager.button_press_start_time);
        }
    }
}

OPERATE_RET gpio_manager_get_status(gpio_status_t *status)
{
    if (!status) {
        return OPRT_INVALID_PARM;
    }

    if (!g_gpio_manager.initialized) {
        memset(status, 0, sizeof(gpio_status_t));
        return OPRT_COM_ERROR;
    }

    status->led_state = g_gpio_manager.system_led_state;
    status->button_state = g_gpio_manager.button_state;
    status->led_physical_state = g_gpio_manager.led_current_state;
    status->led_last_toggle_time = g_gpio_manager.last_blink_time;
    status->button_last_change_time = g_gpio_manager.button_last_read_time;
    status->button_press_start_time = g_gpio_manager.button_press_start_time;
    status->initialized = g_gpio_manager.initialized;

    return OPRT_OK;
}

void gpio_manager_force_led(bool on)
{
    if (!g_gpio_manager.initialized) {
        PR_WARN("⚠️ GPIO管理器未初始化");
        return;
    }

    PR_DEBUG("🔧 强制设置LED: %s", on ? "点亮" : "熄灭");

    tal_gpio_write(GPIO_LED_USER, on ? TUYA_GPIO_LEVEL_HIGH : TUYA_GPIO_LEVEL_LOW);
    g_gpio_manager.led_current_state = on;
}

// ========== 内部函数实现 ==========

static bool read_button_physical_state(void)
{
    TUYA_GPIO_LEVEL_E level;
    OPERATE_RET ret = tal_gpio_read(GPIO_BUTTON_USER, &level);

    if (ret != OPRT_OK) {
        PR_ERR("❌ 读取按键状态失败: %d", ret);
        return false;
    }

    // 上拉输入，按下时为低电平
    return (level == TUYA_GPIO_LEVEL_LOW);
}

static void handle_button_state_change(bool current_state, uint32_t current_time)
{
    if (current_state) {
        // 按键按下
        if (g_gpio_manager.button_state == BUTTON_STATE_RELEASED) {
            g_gpio_manager.button_state = BUTTON_STATE_PRESSED;
            g_gpio_manager.button_press_start_time = current_time;

            PR_DEBUG("🔘 按键按下");
        }
    } else {
        // 按键释放
        if (g_gpio_manager.button_state == BUTTON_STATE_PRESSED ||
            g_gpio_manager.button_state == BUTTON_STATE_LONG_PRESSED) {

            uint32_t press_duration = current_time - g_gpio_manager.button_press_start_time;
            button_event_e event = BUTTON_EVENT_NONE;

            if (g_gpio_manager.button_state == BUTTON_STATE_PRESSED) {
                // 短按
                if (press_duration >= GPIO_DEBOUNCE_TIME_MS) {
                    event = BUTTON_EVENT_CLICK;
                    g_gpio_manager.total_button_clicks++;
                    PR_INFO("🔘 检测到单击事件 (持续时间: %d ms)", press_duration);
                }
            } else {
                // 长按释放
                event = BUTTON_EVENT_RELEASE;
                PR_INFO("🔘 长按释放 (总持续时间: %d ms)", press_duration);
            }

            g_gpio_manager.button_state = BUTTON_STATE_RELEASED;

            // 触发回调
            if (event != BUTTON_EVENT_NONE && g_gpio_manager.button_callback) {
                g_gpio_manager.button_callback(event, press_duration);
            }
        }
    }
}

static const char* get_led_state_name(led_state_e state)
{
    switch (state) {
        case LED_STATE_OFF:        return "关闭";
        case LED_STATE_ON:         return "常亮";
        case LED_STATE_SLOW_BLINK: return "慢闪";
        case LED_STATE_FAST_BLINK: return "快闪";
        default:                   return "未知";
    }
}

static const char* get_button_state_name(button_state_e state)
{
    switch (state) {
        case BUTTON_STATE_RELEASED:     return "释放";
        case BUTTON_STATE_PRESSED:      return "按下";
        case BUTTON_STATE_LONG_PRESSED: return "长按";
        default:                        return "未知";
    }
}
