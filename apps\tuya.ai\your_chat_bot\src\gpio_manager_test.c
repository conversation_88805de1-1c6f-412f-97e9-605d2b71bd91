/**
 * @file gpio_manager_test.c
 * @brief GPIO管理模块测试文件
 * 
 * 提供GPIO管理模块的测试功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "gpio_manager.h"
#include "tkl_output.h"
#include "tal_api.h"
#include <string.h>

// ========== 测试相关常量 ==========

#define TEST_LED_CYCLE_TIME_MS      2000    /**< LED测试周期时间 */
#define TEST_BUTTON_TIMEOUT_MS      10000   /**< 按键测试超时时间 */

// ========== 测试状态变量 ==========

static bool g_test_button_clicked = false;
static bool g_test_button_long_pressed = false;
static uint32_t g_test_button_click_duration = 0;
static uint32_t g_test_button_long_press_duration = 0;

// ========== 测试函数声明 ==========

/**
 * @brief 测试GPIO管理器基本功能
 */
static void test_gpio_basic_functions(void);

/**
 * @brief 测试LED控制功能
 */
static void test_gpio_led_control(void);

/**
 * @brief 测试按键检测功能
 */
static void test_gpio_button_detection(void);

/**
 * @brief 测试状态管理功能
 */
static void test_gpio_status_management(void);

/**
 * @brief 测试回调机制
 */
static void test_gpio_callback_mechanism(void);

/**
 * @brief 按键事件测试回调函数
 * 
 * @param event 按键事件类型
 * @param duration 按键持续时间
 */
static void test_button_event_callback(button_event_e event, uint32_t duration);

/**
 * @brief 等待指定时间并更新GPIO状态
 * 
 * @param wait_time_ms 等待时间(毫秒)
 */
static void wait_and_update(uint32_t wait_time_ms);

// ========== 公共函数实现 ==========

void gpio_manager_run_tests(void)
{
    PR_INFO("🧪 开始GPIO管理器测试");
    PR_INFO("========================================");
    
    // 测试基本功能
    test_gpio_basic_functions();
    
    // 测试LED控制功能
    test_gpio_led_control();
    
    // 测试状态管理功能
    test_gpio_status_management();
    
    // 测试回调机制
    test_gpio_callback_mechanism();
    
    // 测试按键检测功能（需要用户交互）
    test_gpio_button_detection();
    
    PR_INFO("========================================");
    PR_INFO("✅ GPIO管理器测试完成");
}

// ========== 内部测试函数实现 ==========

static void test_gpio_basic_functions(void)
{
    PR_INFO("🔧 测试基本功能...");
    
    // 测试初始化
    OPERATE_RET ret = gpio_manager_init();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 初始化测试通过");
    } else {
        PR_ERR("❌ 初始化测试失败: %d", ret);
        return;
    }
    
    // 测试状态获取
    led_state_e led_state = gpio_manager_get_led_state();
    button_state_e button_state = gpio_manager_get_button_state();
    
    PR_INFO("📊 初始状态:");
    PR_INFO("   - LED状态: %d", led_state);
    PR_INFO("   - 按键状态: %d", button_state);
    
    if (led_state == LED_STATE_SLOW_BLINK && button_state == BUTTON_STATE_RELEASED) {
        PR_INFO("✅ 状态获取测试通过");
    } else {
        PR_ERR("❌ 状态获取测试失败");
    }
    
    PR_INFO("✅ 基本功能测试完成");
}

static void test_gpio_led_control(void)
{
    PR_INFO("💡 测试LED控制功能...");
    
    // 测试所有LED状态
    led_state_e test_states[] = {
        LED_STATE_OFF,
        LED_STATE_ON,
        LED_STATE_SLOW_BLINK,
        LED_STATE_FAST_BLINK
    };
    
    const char* state_names[] = {
        "关闭", "常亮", "慢闪", "快闪"
    };
    
    for (int i = 0; i < 4; i++) {
        PR_INFO("🔄 测试LED状态: %s", state_names[i]);
        
        gpio_manager_set_led_state(test_states[i]);
        led_state_e current_state = gpio_manager_get_led_state();
        
        if (current_state == test_states[i]) {
            PR_INFO("✅ LED状态设置测试通过: %s", state_names[i]);
        } else {
            PR_ERR("❌ LED状态设置测试失败: 期望%d, 实际%d", test_states[i], current_state);
        }
        
        // 等待并观察LED状态变化
        wait_and_update(TEST_LED_CYCLE_TIME_MS);
    }
    
    // 测试强制LED控制
    PR_INFO("🔧 测试强制LED控制...");
    gpio_manager_force_led(true);
    wait_and_update(1000);
    gpio_manager_force_led(false);
    wait_and_update(1000);
    
    PR_INFO("✅ LED控制功能测试完成");
}

static void test_gpio_status_management(void)
{
    PR_INFO("📊 测试状态管理功能...");
    
    gpio_status_t status;
    OPERATE_RET ret = gpio_manager_get_status(&status);
    
    if (ret == OPRT_OK) {
        PR_INFO("✅ 状态获取成功");
        PR_INFO("📋 当前状态信息:");
        PR_INFO("   - 初始化状态: %s", status.initialized ? "是" : "否");
        PR_INFO("   - LED状态: %d", status.led_state);
        PR_INFO("   - 按键状态: %d", status.button_state);
        PR_INFO("   - LED物理状态: %s", status.led_physical_state ? "高" : "低");
        PR_INFO("   - LED最后切换时间: %d", status.led_last_toggle_time);
        PR_INFO("   - 按键最后变化时间: %d", status.button_last_change_time);
    } else {
        PR_ERR("❌ 状态获取失败: %d", ret);
    }
    
    // 测试无效参数
    ret = gpio_manager_get_status(NULL);
    if (ret != OPRT_OK) {
        PR_INFO("✅ 无效参数处理测试通过");
    } else {
        PR_ERR("❌ 无效参数处理测试失败");
    }
    
    PR_INFO("✅ 状态管理功能测试完成");
}

static void test_gpio_callback_mechanism(void)
{
    PR_INFO("📞 测试回调机制...");
    
    // 重置测试状态
    g_test_button_clicked = false;
    g_test_button_long_pressed = false;
    g_test_button_click_duration = 0;
    g_test_button_long_press_duration = 0;
    
    // 注册回调函数
    gpio_manager_register_button_callback(test_button_event_callback);
    
    PR_INFO("✅ 回调函数注册完成");
    PR_INFO("📝 注意: 实际按键事件需要硬件交互才能触发");
    
    // 测试空回调
    gpio_manager_register_button_callback(NULL);
    PR_INFO("✅ 空回调处理测试通过");
    
    // 恢复测试回调
    gpio_manager_register_button_callback(test_button_event_callback);
    
    PR_INFO("✅ 回调机制测试完成");
}

static void test_gpio_button_detection(void)
{
    PR_INFO("🔘 测试按键检测功能...");
    PR_INFO("📝 请在%d秒内按下用户按键(P12)进行测试", TEST_BUTTON_TIMEOUT_MS / 1000);
    
    uint32_t start_time = tal_system_get_millisecond();
    uint32_t timeout = TEST_BUTTON_TIMEOUT_MS;
    
    // 设置LED为快闪，提示用户进行按键测试
    gpio_manager_set_led_state(LED_STATE_FAST_BLINK);
    
    while ((tal_system_get_millisecond() - start_time) < timeout) {
        gpio_manager_update();
        
        // 检查是否有按键事件
        if (g_test_button_clicked || g_test_button_long_pressed) {
            break;
        }
        
        tal_system_sleep(10);
    }
    
    // 恢复LED状态
    gpio_manager_set_led_state(LED_STATE_SLOW_BLINK);
    
    // 报告测试结果
    if (g_test_button_clicked) {
        PR_INFO("✅ 单击事件检测成功 (持续时间: %d ms)", g_test_button_click_duration);
    }
    
    if (g_test_button_long_pressed) {
        PR_INFO("✅ 长按事件检测成功 (持续时间: %d ms)", g_test_button_long_press_duration);
    }
    
    if (!g_test_button_clicked && !g_test_button_long_pressed) {
        PR_WARN("⚠️ 未检测到按键事件 (可能是用户未操作或硬件问题)");
    }
    
    PR_INFO("✅ 按键检测功能测试完成");
}

// ========== 辅助函数实现 ==========

static void test_button_event_callback(button_event_e event, uint32_t duration)
{
    switch (event) {
        case BUTTON_EVENT_CLICK:
            g_test_button_clicked = true;
            g_test_button_click_duration = duration;
            PR_INFO("🔘 回调: 检测到单击事件 (持续时间: %d ms)", duration);
            break;
            
        case BUTTON_EVENT_LONG_PRESS:
            g_test_button_long_pressed = true;
            g_test_button_long_press_duration = duration;
            PR_INFO("🔘 回调: 检测到长按事件 (持续时间: %d ms)", duration);
            break;
            
        case BUTTON_EVENT_RELEASE:
            PR_INFO("🔘 回调: 检测到释放事件 (持续时间: %d ms)", duration);
            break;
            
        default:
            PR_WARN("🔘 回调: 未知事件类型 %d", event);
            break;
    }
}

static void wait_and_update(uint32_t wait_time_ms)
{
    uint32_t start_time = tal_system_get_millisecond();
    
    while ((tal_system_get_millisecond() - start_time) < wait_time_ms) {
        gpio_manager_update();
        tal_system_sleep(10);
    }
}
