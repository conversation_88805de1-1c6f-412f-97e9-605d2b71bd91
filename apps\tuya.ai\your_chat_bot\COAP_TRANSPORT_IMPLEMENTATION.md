# 4.3 CoAP传输层协议实现

## 4.3.1 CoAP协议概述

在本智能药盒项目中，设备状态监控数据、传感器读数、电池电量信息等轻量级数据通过CoAP (Constrained Application Protocol)[1] 受限应用协议进行传输。CoAP是一种专为物联网设备设计的应用层协议，基于UDP传输，具有低功耗、低延迟的特性。它是RESTful架构在受限环境下的实现，使资源受限的设备能够高效地与云端服务器进行通信。

CoAP消息类型包括CON（可靠消息）、NON（不可靠消息）、ACK（确认消息）、RST（重置消息）等，在本项目中主要使用CON和NON消息类型。

```c
// CoAP消息类型定义
typedef enum {
    COAP_TYPE_CON = 0,    // 可靠消息，需要ACK确认
    COAP_TYPE_NON = 1,    // 不可靠消息，无需确认
    COAP_TYPE_ACK = 2,    // 确认消息
    COAP_TYPE_RST = 3     // 重置消息
} coap_message_type_t;

// CoAP请求方法
typedef enum {
    COAP_METHOD_GET = 1,     // 获取资源
    COAP_METHOD_POST = 2,    // 创建资源
    COAP_METHOD_PUT = 3,     // 更新资源
    COAP_METHOD_DELETE = 4   // 删除资源
} coap_method_t;
```

## 4.3.2 CoAP客户端配置与连接

本项目通过libcoap库实现CoAP客户端功能，与涂鸦IoT云平台的CoAP网关建立通信。CoAP基于UDP协议，无需建立持久连接，每次通信都是独立的请求-响应模式，这大大降低了设备的功耗和内存占用。

```c
// CoAP客户端全局配置
static coap_client_config_t g_coap_config = {
    .server_host = "coap.tuyacn.com",      // 涂鸦CoAP服务器
    .server_port = 5683,                    // 标准CoAP端口
    .max_retransmit = 3,                    // 最大重传次数
    .ack_timeout_ms = 2000,                 // ACK超时时间2秒
    .max_latency_ms = 100000,               // 最大延迟100秒
    .block_size = 64,                       // 块传输大小64字节
    .enable_dtls = false,                   // 暂不启用DTLS加密
};

// CoAP客户端初始化
OPERATE_RET coap_client_init(void) {
    // 初始化CoAP上下文
    g_coap_context = coap_new_context(NULL);
    if (!g_coap_context) {
        PR_ERR("CoAP上下文创建失败");
        return OPRT_MALLOC_FAILED;
    }
    
    // 设置服务器地址
    coap_address_t server_addr;
    coap_address_init(&server_addr);
    server_addr.addr.sin.sin_family = AF_INET;
    server_addr.addr.sin.sin_port = htons(g_coap_config.server_port);
    inet_pton(AF_INET, g_coap_config.server_host, &server_addr.addr.sin.sin_addr);
    
    // 创建客户端会话
    g_coap_session = coap_new_client_session(g_coap_context, NULL, &server_addr, COAP_PROTO_UDP);
    if (!g_coap_session) {
        PR_ERR("CoAP会话创建失败");
        coap_free_context(g_coap_context);
        return OPRT_COM_ERROR;
    }
    
    PR_INFO("CoAP客户端初始化成功，服务器: %s:%d", 
            g_coap_config.server_host, g_coap_config.server_port);
    return OPRT_OK;
}
```

## 4.3.3 设备状态数据上报

智能药盒通过CoAP协议定期向云端上报设备状态信息，包括电池电量、网络信号强度、存储空间使用情况、各药仓剩余药量等。由于这些数据对实时性要求不高，且数据量较小，使用CoAP的NON消息类型可以有效降低网络开销和设备功耗。

```c
// 设备状态数据结构
typedef struct {
    uint8_t battery_level;          // 电池电量百分比
    int8_t wifi_rssi;              // WiFi信号强度
    uint16_t free_heap_size;       // 可用堆内存KB
    uint8_t medication_levels[6];   // 6个药仓剩余药量百分比
    uint32_t uptime_seconds;       // 设备运行时间秒数
    uint8_t error_flags;           // 错误标志位
} device_status_t;

// 上报设备状态到CoAP服务器
OPERATE_RET coap_report_device_status(void) {
    // 收集设备状态数据
    device_status_t status;
    collect_device_status(&status);
    
    // 创建CoAP PDU
    coap_pdu_t *pdu = coap_pdu_init(COAP_MESSAGE_NON,           // 不可靠消息
                                   COAP_REQUEST_POST,            // POST方法
                                   coap_new_message_id(g_coap_session),
                                   coap_session_max_pdu_size(g_coap_session));
    if (!pdu) {
        PR_ERR("CoAP PDU创建失败");
        return OPRT_MALLOC_FAILED;
    }
    
    // 设置URI路径
    const char *uri_path = "device/status";
    coap_add_option(pdu, COAP_OPTION_URI_PATH, strlen(uri_path), 
                   (const uint8_t *)uri_path);
    
    // 设置Content-Format为application/json
    uint16_t content_format = COAP_MEDIATYPE_APPLICATION_JSON;
    coap_add_option(pdu, COAP_OPTION_CONTENT_FORMAT, 
                   sizeof(content_format), (const uint8_t *)&content_format);
    
    // 构造JSON载荷
    char json_payload[256];
    snprintf(json_payload, sizeof(json_payload),
            "{"
            "\"battery\":%d,"
            "\"rssi\":%d,"
            "\"heap\":%d,"
            "\"medications\":[%d,%d,%d,%d,%d,%d],"
            "\"uptime\":%lu,"
            "\"errors\":%d"
            "}",
            status.battery_level, status.wifi_rssi, status.free_heap_size,
            status.medication_levels[0], status.medication_levels[1],
            status.medication_levels[2], status.medication_levels[3],
            status.medication_levels[4], status.medication_levels[5],
            status.uptime_seconds, status.error_flags);
    
    // 添加载荷数据
    coap_add_data(pdu, strlen(json_payload), (const uint8_t *)json_payload);
    
    // 发送CoAP消息
    coap_mid_t message_id = coap_send(g_coap_session, pdu);
    if (message_id == COAP_INVALID_MID) {
        PR_ERR("CoAP消息发送失败");
        return OPRT_COM_ERROR;
    }
    
    PR_DEBUG("设备状态已通过CoAP上报，消息ID: %d", message_id);
    return OPRT_OK;
}
```

## 4.3.4 传感器数据批量传输

对于温湿度传感器、光照传感器等产生的连续数据，本项目采用CoAP的块传输（Block Transfer）机制进行批量上传。这种方式可以在单个CoAP事务中传输大量数据，同时保持协议的轻量级特性。

```c
// 传感器数据点结构
typedef struct {
    uint32_t timestamp;        // 时间戳
    float temperature;         // 温度值
    float humidity;           // 湿度值
    uint16_t light_level;     // 光照强度
} sensor_data_point_t;

// 批量上传传感器数据
OPERATE_RET coap_upload_sensor_data_batch(sensor_data_point_t *data_points, 
                                         int count) {
    if (!data_points || count <= 0) {
        return OPRT_INVALID_PARM;
    }
    
    // 构造JSON数组
    char *json_buffer = malloc(count * 100 + 100);  // 预估每个数据点100字节
    if (!json_buffer) {
        PR_ERR("内存分配失败");
        return OPRT_MALLOC_FAILED;
    }
    
    strcpy(json_buffer, "{\"sensor_data\":[");
    
    for (int i = 0; i < count; i++) {
        char data_json[100];
        snprintf(data_json, sizeof(data_json),
                "%s{\"ts\":%lu,\"temp\":%.2f,\"hum\":%.2f,\"light\":%d}",
                (i > 0) ? "," : "",
                data_points[i].timestamp,
                data_points[i].temperature,
                data_points[i].humidity,
                data_points[i].light_level);
        strcat(json_buffer, data_json);
    }
    strcat(json_buffer, "]}");
    
    size_t payload_len = strlen(json_buffer);
    
    // 创建CoAP PDU，使用CON消息确保可靠传输
    coap_pdu_t *pdu = coap_pdu_init(COAP_MESSAGE_CON,
                                   COAP_REQUEST_POST,
                                   coap_new_message_id(g_coap_session),
                                   coap_session_max_pdu_size(g_coap_session));
    
    // 设置URI路径
    const char *uri_path = "sensor/batch";
    coap_add_option(pdu, COAP_OPTION_URI_PATH, strlen(uri_path),
                   (const uint8_t *)uri_path);
    
    // 设置Content-Format
    uint16_t content_format = COAP_MEDIATYPE_APPLICATION_JSON;
    coap_add_option(pdu, COAP_OPTION_CONTENT_FORMAT,
                   sizeof(content_format), (const uint8_t *)&content_format);
    
    // 如果数据量大，启用块传输
    if (payload_len > g_coap_config.block_size) {
        PR_INFO("启用CoAP块传输，数据大小: %d字节", payload_len);
        
        // 设置Block1选项，表示客户端发送块数据
        uint32_t block_option = 0;  // 第一个块，64字节大小
        coap_add_option(pdu, COAP_OPTION_BLOCK1, 
                       coap_encode_var_safe(block_option), 
                       (const uint8_t *)&block_option);
    }
    
    // 添加载荷数据
    coap_add_data(pdu, payload_len, (const uint8_t *)json_buffer);
    
    // 注册响应处理回调
    coap_register_response_handler(g_coap_context, coap_sensor_data_response_handler);
    
    // 发送消息
    coap_mid_t message_id = coap_send(g_coap_session, pdu);
    
    free(json_buffer);
    
    if (message_id == COAP_INVALID_MID) {
        PR_ERR("传感器数据批量上传失败");
        return OPRT_COM_ERROR;
    }
    
    PR_INFO("传感器数据批量上传成功，%d个数据点，消息ID: %d", count, message_id);
    return OPRT_OK;
}

// CoAP响应处理回调
static void coap_sensor_data_response_handler(coap_context_t *ctx,
                                             coap_session_t *session,
                                             coap_pdu_t *sent,
                                             coap_pdu_t *received,
                                             const coap_mid_t mid) {
    coap_response_code_t response_code = coap_get_code(received);
    
    if (response_code == COAP_RESPONSE_CODE_CREATED ||
        response_code == COAP_RESPONSE_CODE_CHANGED) {
        PR_INFO("传感器数据上传成功，响应码: %d.%02d", 
                response_code >> 5, response_code & 0x1F);
    } else {
        PR_ERR("传感器数据上传失败，响应码: %d.%02d",
               response_code >> 5, response_code & 0x1F);
    }
}
```

## 4.3.5 CoAP资源发现与服务注册

智能药盒启动时通过CoAP的资源发现机制向云端注册自身提供的服务和资源。这包括设备基本信息、支持的功能列表、可用的CoAP资源路径等。云端可以通过GET /.well-known/core请求获取设备的完整资源描述。

```c
// 设备资源描述结构
typedef struct {
    const char *path;           // 资源路径
    const char *content_type;   // 内容类型
    const char *interface;      // 接口类型
    const char *resource_type;  // 资源类型
    bool observable;            // 是否可观察
} coap_resource_desc_t;

// 设备支持的CoAP资源列表
static const coap_resource_desc_t g_device_resources[] = {
    {"/device/info", "application/json", "if.r", "rt.device", false},
    {"/device/status", "application/json", "if.rw", "rt.status", true},
    {"/sensor/data", "application/json", "if.r", "rt.sensor", true},
    {"/medication/dispense", "application/json", "if.w", "rt.action", false},
    {"/config/update", "application/json", "if.w", "rt.config", false},
    {NULL, NULL, NULL, NULL, false}  // 结束标记
};

// 向云端注册设备资源
OPERATE_RET coap_register_device_resources(void) {
    // 构造资源描述JSON
    char resource_json[1024];
    strcpy(resource_json, "{\"resources\":[");
    
    for (int i = 0; g_device_resources[i].path != NULL; i++) {
        char resource_item[200];
        snprintf(resource_item, sizeof(resource_item),
                "%s{\"path\":\"%s\",\"ct\":\"%s\",\"if\":\"%s\",\"rt\":\"%s\",\"obs\":%s}",
                (i > 0) ? "," : "",
                g_device_resources[i].path,
                g_device_resources[i].content_type,
                g_device_resources[i].interface,
                g_device_resources[i].resource_type,
                g_device_resources[i].observable ? "true" : "false");
        strcat(resource_json, resource_item);
    }
    strcat(resource_json, "]}");
    
    // 创建注册请求PDU
    coap_pdu_t *pdu = coap_pdu_init(COAP_MESSAGE_CON,
                                   COAP_REQUEST_POST,
                                   coap_new_message_id(g_coap_session),
                                   coap_session_max_pdu_size(g_coap_session));
    
    // 设置注册URI
    const char *register_uri = "device/register";
    coap_add_option(pdu, COAP_OPTION_URI_PATH, strlen(register_uri),
                   (const uint8_t *)register_uri);
    
    // 添加设备ID作为URI查询参数
    char device_query[64];
    snprintf(device_query, sizeof(device_query), "id=%s", get_device_id());
    coap_add_option(pdu, COAP_OPTION_URI_QUERY, strlen(device_query),
                   (const uint8_t *)device_query);
    
    // 设置Content-Format
    uint16_t content_format = COAP_MEDIATYPE_APPLICATION_JSON;
    coap_add_option(pdu, COAP_OPTION_CONTENT_FORMAT,
                   sizeof(content_format), (const uint8_t *)&content_format);
    
    // 添加载荷
    coap_add_data(pdu, strlen(resource_json), (const uint8_t *)resource_json);
    
    // 发送注册请求
    coap_mid_t message_id = coap_send(g_coap_session, pdu);
    
    if (message_id == COAP_INVALID_MID) {
        PR_ERR("设备资源注册失败");
        return OPRT_COM_ERROR;
    }
    
    PR_INFO("设备资源注册请求已发送，消息ID: %d", message_id);
    return OPRT_OK;
}
```

## 4.3.6 CoAP观察者模式实现

对于需要实时监控的数据（如药品余量、设备状态等），本项目实现了CoAP的观察者（Observer）模式。云端可以订阅特定资源的变化通知，当资源状态发生改变时，设备会主动推送更新信息，实现了高效的实时数据同步。

```c
// 观察者列表管理
typedef struct observer_node {
    coap_session_t *session;
    uint8_t token[8];
    size_t token_len;
    uint32_t observe_value;
    struct observer_node *next;
} observer_node_t;

static observer_node_t *g_observers = NULL;

// 处理观察请求
void coap_handle_observe_request(coap_context_t *ctx,
                                coap_resource_t *resource,
                                coap_session_t *session,
                                coap_pdu_t *request,
                                coap_binary_t *token,
                                coap_string_t *query,
                                coap_pdu_t *response) {
    // 检查是否包含Observe选项
    coap_opt_t *observe_opt = coap_check_option(request, COAP_OPTION_OBSERVE, NULL);
    if (observe_opt) {
        uint32_t observe_value = coap_decode_var_bytes(coap_opt_value(observe_opt),
                                                      coap_opt_length(observe_opt));
        
        if (observe_value == 0) {  // 注册观察者
            // 添加到观察者列表
            observer_node_t *new_observer = malloc(sizeof(observer_node_t));
            new_observer->session = session;
            memcpy(new_observer->token, token->s, token->length);
            new_observer->token_len = token->length;
            new_observer->observe_value = 1;  // 初始观察值
            new_observer->next = g_observers;
            g_observers = new_observer;
            
            PR_INFO("新增观察者，Token长度: %d", token->length);
            
            // 在响应中添加Observe选项
            coap_add_option(response, COAP_OPTION_OBSERVE,
                           coap_encode_var_safe(new_observer->observe_value),
                           (const uint8_t *)&new_observer->observe_value);
        } else if (observe_value == 1) {  // 取消观察
            // 从观察者列表中移除
            remove_observer(session, token);
            PR_INFO("移除观察者");
        }
    }
    
    // 设置响应内容
    const char *current_status = get_current_device_status_json();
    coap_add_data(response, strlen(current_status), (const uint8_t *)current_status);
    coap_set_code(response, COAP_RESPONSE_CODE_CONTENT);
}

// 通知所有观察者
void coap_notify_observers(const char *resource_path, const char *new_data) {
    observer_node_t *current = g_observers;
    
    while (current != NULL) {
        // 创建通知PDU
        coap_pdu_t *notify_pdu = coap_pdu_init(COAP_MESSAGE_CON,
                                              COAP_RESPONSE_CODE_CONTENT,
                                              coap_new_message_id(current->session),
                                              coap_session_max_pdu_size(current->session));
        
        // 添加Token
        coap_add_token(notify_pdu, current->token_len, current->token);
        
        // 添加Observe选项，递增观察值
        current->observe_value++;
        coap_add_option(notify_pdu, COAP_OPTION_OBSERVE,
                       coap_encode_var_safe(current->observe_value),
                       (const uint8_t *)&current->observe_value);
        
        // 添加载荷数据
        coap_add_data(notify_pdu, strlen(new_data), (const uint8_t *)new_data);
        
        // 发送通知
        coap_send(current->session, notify_pdu);
        
        current = current->next;
    }
    
    PR_INFO("已通知%d个观察者资源变化: %s", count_observers(), resource_path);
}
```

---
*参考文献:*  
*[1] Shelby, Z., Hartke, K., & Bormann, C. (2014). The Constrained Application Protocol (CoAP). RFC 7252.*
