# 服药日志管理模块使用指南

## 概述

服药日志管理模块提供了完整的服药记录存储、管理和云端同步功能。该模块基于涂鸦DP协议，通过DP118实现服药记录的云端上传，支持Flash本地存储和JSON格式化。

## 功能特性

### 1. Flash本地存储
- 64KB专用存储空间，支持100条记录循环存储
- CRC32数据完整性校验
- 断电数据保护，重启后数据不丢失
- 支持记录的增删改查操作

### 2. DP118云端同步
- 基于涂鸦DP协议的可靠数据传输
- JSON格式化服药记录数据
- 批量上传机制，每次最多5条记录
- 自动重试和错误处理

### 3. 智能数据管理
- 自动上传模式，新记录自动标记为待上传
- 上传状态跟踪，区分已上传和待上传记录
- 完整的统计信息，包括成功率和失败率
- 支持强制重新上传所有记录

## DP118配置说明

根据涂鸦DP协议文档，DP118配置如下：

```json
{
  "id": 118,
  "code": "record",
  "name": "服药记录缓存", 
  "mode": "rw",
  "type": "string",
  "maxlen": 255,
  "desc": "用于上传服药记录的JSON数据"
}
```

**DP类型说明**：
- **数据类型**：字符型(string) - PROP_STR
- **传输类型**：可上报下发(RW) - 支持设备上报和云端查询
- **时效性**：记录型DP - 带时间戳的历史数据记录
- **最大长度**：255字节 - 限制单次上传的JSON数据大小

## 使用方法

### 1. 初始化模块

```c
#include "medication_log_manager.h"

// 在系统初始化时调用
OPERATE_RET ret = medication_log_manager_init();
if (ret != OPRT_OK) {
    PR_ERR("服药日志管理器初始化失败: %d", ret);
    return ret;
}
```

### 2. 记录服药日志

```c
// 记录服药事件
OPERATE_RET ret = medication_log_manager_write_dispense("阿司匹林");
if (ret == OPRT_OK) {
    PR_INFO("服药记录已保存");
} else {
    PR_ERR("服药记录保存失败: %d", ret);
}

// 记录多种药品
const char* medications[] = {"二甲双胍", "氨氯地平", "维生素D3"};
for (int i = 0; i < 3; i++) {
    medication_log_manager_write_dispense(medications[i]);
    tal_system_sleep(100); // 确保时间戳不同
}
```

### 3. 上传到DP118

```c
// 手动上传待上传的记录
OPERATE_RET ret = medication_log_manager_upload_records();
if (ret == OPRT_OK) {
    PR_INFO("服药记录上传成功");
} else {
    PR_ERR("服药记录上传失败: %d", ret);
}

// 强制上传所有记录(包括已上传的)
ret = medication_log_manager_force_upload_all();
```

### 4. 自动上传模式

```c
// 启用自动上传
medication_log_manager_set_auto_upload(true);

// 在主循环中定期调用
void main_loop(void)
{
    while (1) {
        // 处理自动上传任务
        medication_log_manager_update();
        
        // 其他业务逻辑
        tal_system_sleep(1000);
    }
}
```

### 5. 查询统计信息

```c
// 获取统计信息
medication_log_stats_t stats;
OPERATE_RET ret = medication_log_manager_get_stats(&stats);
if (ret == OPRT_OK) {
    PR_INFO("总记录数: %d", stats.total_records);
    PR_INFO("已上传: %d", stats.uploaded_records);
    PR_INFO("待上传: %d", stats.pending_records);
    PR_INFO("上传成功次数: %d", stats.upload_success_count);
    PR_INFO("上传失败次数: %d", stats.upload_failed_count);
}
```

### 6. 查看最近记录

```c
// 获取最近10条记录
char recent_records[10][128];
int actual_count = 0;

OPERATE_RET ret = medication_log_manager_get_recent_records(recent_records, 10, &actual_count);
if (ret == OPRT_OK) {
    for (int i = 0; i < actual_count; i++) {
        PR_INFO("记录%d: %s", i + 1, recent_records[i]);
    }
}

// 或者直接打印最近记录
medication_log_manager_print_recent();
```

### 7. 状态显示

```c
// 显示状态信息(会通过DP108发送到前端)
medication_log_manager_display_status("系统正常运行");

// 显示成功信息
medication_log_manager_display_success("服药记录同步完成");
```

## JSON数据格式

上传到DP118的JSON数据格式如下：

```json
[
  {
    "timestamp": 1703318400,
    "medication": "阿司匹林",
    "log": "2024-12-23 08:00 服用 阿司匹林",
    "uploaded": false
  },
  {
    "timestamp": 1703322000,
    "medication": "二甲双胍", 
    "log": "2024-12-23 09:00 服用 二甲双胍",
    "uploaded": false
  }
]
```

**字段说明**：
- `timestamp`: POSIX时间戳，记录服药时间
- `medication`: 药品名称，最大31字符
- `log`: 格式化的日志内容，包含时间和药品信息
- `uploaded`: 上传状态标志

## 技术实现细节

### Flash存储结构

```
Flash地址: 0x1F0000 - 0x200000 (64KB)
├── 头部信息 (48字节)
│   ├── 魔数 (4字节): 0x4D454449 ("MEDI")
│   ├── 版本号 (4字节): 1
│   ├── 记录数量 (4字节)
│   ├── 写入索引 (4字节)
│   └── 最后上传索引 (4字节)
└── 记录数据 (48字节 × 100条)
    ├── 魔数 (4字节)
    ├── 时间戳 (4字节)
    ├── 药品名称 (32字节)
    ├── 日志内容 (64字节)
    ├── CRC校验 (4字节)
    └── 上传标志 (1字节)
```

### DP协议集成

模块使用涂鸦标准DP协议接口：

```c
// 构建DP数据结构
TY_OBJ_DP_S dp_data;
dp_data.dpid = 118;                    // DP118
dp_data.type = PROP_STR;               // 字符串类型
dp_data.value.dp_str = json_data;      // JSON数据
dp_data.time_stamp = 0;                // 实时型DP

// 同步上报到云端
OPERATE_RET ret = dev_report_dp_stat_sync(NULL, &dp_data, 1, 5);
```

## 测试功能

模块提供了完整的测试功能：

```c
// 运行完整测试
medication_log_manager_run_tests();
```

测试内容包括：
- 基本功能测试（初始化、状态显示）
- Flash存储功能测试（读写、CRC校验）
- JSON格式化测试（数据格式验证）
- DP118上传测试（网络通信）
- 统计信息测试（数据统计）
- 自动上传测试（后台任务）
- 错误处理测试（异常情况）

## 集成到主程序

```c
// 在tuya_main.c中添加
#include "medication_log_manager.h"

// 在系统初始化函数中
static void system_init(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化服药日志管理器
    OPERATE_RET ret = medication_log_manager_init();
    if (ret != OPRT_OK) {
        PR_ERR("服药日志管理器初始化失败");
        return;
    }
    
    // 启用自动上传
    medication_log_manager_set_auto_upload(true);
    
    // 运行测试（可选，仅在调试时使用）
    #ifdef DEBUG_MEDICATION_LOG
    medication_log_manager_run_tests();
    #endif
}

// 在主循环中
static void main_loop(void)
{
    while (1) {
        // 更新服药日志管理器
        medication_log_manager_update();
        
        // 其他业务逻辑
        tal_system_sleep(1000);
    }
}

// 在分药成功后记录日志
static void on_medication_dispensed(const char* medication_name)
{
    medication_log_manager_write_dispense(medication_name);
    medication_log_manager_display_success("服药记录已保存");
}
```

## 注意事项

1. **初始化顺序**：确保在IoT通信管理器初始化后再初始化本模块
2. **Flash空间管理**：100条记录循环存储，旧记录会被新记录覆盖
3. **网络依赖**：DP118上传需要网络连接，离线时记录会缓存在本地
4. **JSON长度限制**：单次上传最大255字节，超出时会截断记录
5. **时间戳精度**：使用POSIX时间戳，精度为秒级
6. **CRC校验**：所有Flash记录都有CRC32校验，确保数据完整性

## 错误处理

模块会返回标准的TuyaOpen错误码：
- `OPRT_OK`：操作成功
- `OPRT_INVALID_PARM`：参数无效
- `OPRT_COM_ERROR`：通信错误
- `OPRT_MALLOC_FAILED`：内存分配失败
- 其他：具体的Flash或网络错误码

## 性能考虑

- Flash写入频率：每次服药记录一次，正常使用下每天几次
- 网络上传频率：自动模式下每60秒检查一次待上传记录
- 内存占用：约2KB RAM用于缓存和临时数据
- CPU占用：主要在JSON格式化和CRC计算时，占用时间<10ms
