# GPIO管理模块使用指南

## 概述

GPIO管理模块提供了完整的LED控制系统、按键管理和GPIO状态更新功能。该模块从1223.md文件中的第221-316行提取并独立实现。

## 功能特性

### 1. LED控制系统
- 支持4种LED状态：关闭、常亮、慢闪(1秒)、快闪(0.2秒)
- 自动闪烁管理，无需外部定时器
- 强制LED控制功能

### 2. 按键管理
- 支持按键防抖处理(50ms)
- 长按检测(2秒)
- 按键事件回调机制
- 按键状态实时监控

### 3. GPIO状态更新
- 实时状态监控和更新
- 完整的状态信息查询
- 统计信息管理

## 硬件配置

### GPIO引脚定义

```c
#define GPIO_LED_USER               TUYA_GPIO_NUM_1     // P01 - 开发板User LED
#define GPIO_BUTTON_USER            TUYA_GPIO_NUM_12    // P12 - 开发板User按键
#define SERVO_PWM_PIN_1             TUYA_GPIO_NUM_6     // P06 - pwm0:0
#define SERVO_PWM_PIN_2             TUYA_GPIO_NUM_7     // P07 - pwm0:1
#define SERVO_PWM_PIN_3             TUYA_GPIO_NUM_8     // P08 - pwm0:2
```

### 配置参数

```c
#define GPIO_DEBOUNCE_TIME_MS       50      // 按键防抖时间(毫秒)
#define GPIO_LONG_PRESS_TIME_MS     2000    // 长按检测时间(毫秒)
#define GPIO_BLINK_SLOW_INTERVAL    1000    // 慢闪间隔(毫秒)
#define GPIO_BLINK_FAST_INTERVAL    200     // 快闪间隔(毫秒)
```

## 使用方法

### 1. 初始化模块

```c
#include "gpio_manager.h"

// 在系统初始化时调用
OPERATE_RET ret = gpio_manager_init();
if (ret != OPRT_OK) {
    PR_ERR("GPIO管理器初始化失败: %d", ret);
    return ret;
}
```

### 2. LED控制

```c
// 设置LED状态
gpio_manager_set_led_state(LED_STATE_ON);        // 常亮
gpio_manager_set_led_state(LED_STATE_OFF);       // 关闭
gpio_manager_set_led_state(LED_STATE_SLOW_BLINK); // 慢闪
gpio_manager_set_led_state(LED_STATE_FAST_BLINK); // 快闪

// 获取当前LED状态
led_state_e current_state = gpio_manager_get_led_state();

// 强制设置LED（忽略当前状态）
gpio_manager_force_led(true);   // 强制点亮
gpio_manager_force_led(false);  // 强制熄灭
```

### 3. 按键检测

```c
// 获取按键状态
button_state_e button_state = gpio_manager_get_button_state();

// 注册按键事件回调
void my_button_callback(button_event_e event, uint32_t duration)
{
    switch (event) {
        case BUTTON_EVENT_CLICK:
            PR_INFO("按键单击，持续时间: %d ms", duration);
            break;
        case BUTTON_EVENT_LONG_PRESS:
            PR_INFO("按键长按，持续时间: %d ms", duration);
            break;
        case BUTTON_EVENT_RELEASE:
            PR_INFO("按键释放，总持续时间: %d ms", duration);
            break;
    }
}

gpio_manager_register_button_callback(my_button_callback);
```

### 4. 状态管理

```c
// 获取完整状态信息
gpio_status_t status;
OPERATE_RET ret = gpio_manager_get_status(&status);
if (ret == OPRT_OK) {
    PR_INFO("LED状态: %d", status.led_state);
    PR_INFO("按键状态: %d", status.button_state);
    PR_INFO("LED物理状态: %s", status.led_physical_state ? "高" : "低");
}
```

### 5. 主循环更新

```c
// 在主循环中定期调用
void main_loop(void)
{
    while (1) {
        // 更新GPIO状态（处理LED闪烁和按键检测）
        gpio_manager_update();
        
        // 其他业务逻辑
        // ...
        
        tal_system_sleep(10); // 10ms延时
    }
}
```

### 6. 清理资源

```c
// 程序退出时清理资源
gpio_manager_cleanup();
```

## LED状态说明

| 状态 | 枚举值 | 说明 | 使用场景 |
|------|--------|------|----------|
| 关闭 | LED_STATE_OFF | LED熄灭 | 系统休眠、省电模式 |
| 常亮 | LED_STATE_ON | LED常亮 | 系统正常运行 |
| 慢闪 | LED_STATE_SLOW_BLINK | 1秒间隔闪烁 | 待机状态、网络连接中 |
| 快闪 | LED_STATE_FAST_BLINK | 0.2秒间隔闪烁 | 警告状态、配置模式 |

## 按键事件说明

| 事件 | 枚举值 | 触发条件 | 使用场景 |
|------|--------|----------|----------|
| 单击 | BUTTON_EVENT_CLICK | 按下并释放，持续时间<2秒 | 功能切换、确认操作 |
| 长按 | BUTTON_EVENT_LONG_PRESS | 按下持续时间≥2秒 | 进入配置模式、重置功能 |
| 释放 | BUTTON_EVENT_RELEASE | 长按后释放 | 长按操作完成确认 |

## 测试功能

模块提供了完整的测试功能：

```c
// 运行完整测试
gpio_manager_run_tests();
```

测试内容包括：
- 基本功能测试（初始化、状态获取）
- LED控制功能测试（所有状态切换）
- 按键检测功能测试（需要用户交互）
- 状态管理功能测试
- 回调机制测试

## 集成到主程序

在主程序中集成GPIO管理模块：

```c
// 在tuya_main.c中添加
#include "gpio_manager.h"

// 按键事件处理函数
static void system_button_callback(button_event_e event, uint32_t duration)
{
    switch (event) {
        case BUTTON_EVENT_CLICK:
            // 处理单击事件
            PR_INFO("系统按键单击");
            break;
            
        case BUTTON_EVENT_LONG_PRESS:
            // 处理长按事件（如进入配置模式）
            PR_INFO("系统按键长按，进入配置模式");
            break;
    }
}

// 在系统初始化函数中
static void system_init(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化GPIO管理器
    OPERATE_RET ret = gpio_manager_init();
    if (ret != OPRT_OK) {
        PR_ERR("GPIO管理器初始化失败");
        return;
    }
    
    // 注册按键回调
    gpio_manager_register_button_callback(system_button_callback);
    
    // 设置初始LED状态
    gpio_manager_set_led_state(LED_STATE_SLOW_BLINK);
    
    // 运行测试（可选，仅在调试时使用）
    #ifdef DEBUG_GPIO_MANAGER
    gpio_manager_run_tests();
    #endif
}

// 在主循环中
static void main_loop(void)
{
    while (1) {
        // 更新GPIO状态
        gpio_manager_update();
        
        // 其他业务逻辑
        // ...
        
        tal_system_sleep(10);
    }
}

// 在系统清理函数中
static void system_cleanup(void)
{
    gpio_manager_cleanup();
}
```

## 系统状态指示

建议的LED状态指示方案：

```c
// 系统启动
gpio_manager_set_led_state(LED_STATE_FAST_BLINK);

// 网络连接中
gpio_manager_set_led_state(LED_STATE_SLOW_BLINK);

// 系统正常运行
gpio_manager_set_led_state(LED_STATE_ON);

// 系统错误
gpio_manager_set_led_state(LED_STATE_FAST_BLINK);

// 系统休眠
gpio_manager_set_led_state(LED_STATE_OFF);
```

## 注意事项

1. **初始化顺序**：确保在使用任何功能前先调用初始化函数
2. **主循环更新**：必须在主循环中定期调用`gpio_manager_update()`
3. **硬件配置**：确保GPIO引脚配置与硬件设计匹配
4. **防抖处理**：按键防抖时间可根据硬件特性调整
5. **回调函数**：回调函数中避免长时间阻塞操作
6. **资源管理**：程序退出时调用清理函数释放资源

## 错误处理

模块会返回标准的TuyaOpen错误码：
- `OPRT_OK`：操作成功
- `OPRT_INVALID_PARM`：参数无效
- `OPRT_COM_ERROR`：通信错误
- 其他：具体的GPIO操作错误码

## 性能考虑

- LED闪烁通过软件定时器实现，精度约±10ms
- 按键检测频率为100Hz（每10ms检测一次）
- 防抖处理确保按键事件的可靠性
- 回调机制避免轮询，提高系统效率
