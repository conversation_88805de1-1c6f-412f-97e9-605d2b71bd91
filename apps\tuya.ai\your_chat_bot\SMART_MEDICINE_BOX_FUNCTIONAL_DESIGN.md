# 智能药盒功能设计文档

## 1. 项目概述

基于TuyaOpen平台开发的智能药盒系统，通过IoT技术和智能提醒算法，为用户提供精准的用药管理和健康监护服务。系统采用模块化架构，集成多传感器感知、云端数据同步、智能分药控制等核心功能。

**设计目标**:
- 精准用药管理，确保按时按量服药
- 智能健康监护，实时监控用药状态
- 安全可靠运行，多重保障机制
- 便捷用户体验，支持多种控制方式

**技术架构**:
```
云端管理平台 (涂鸦IoT云 + 移动端APP)
    ↓
通信管理层 (IoT通信管理 + DP数据同步)
    ↓
业务逻辑层 (用药管理 + 日志记录 + 调度控制)
    ↓
硬件控制层 (GPIO管理 + 舵机控制 + 传感器接口)
    ↓
TuyaOpen SDK (TAL API + TKL驱动层)
```

## 2. 核心功能设计

### 2.1 智能用药管理系统

**药品信息管理**: 支持6个独立药品仓位，通过DP101-106管理配置信息。每个仓位包含药品名称、服用频率、方法、注意事项、服用记录等完整信息，支持云端实时同步。

```c
typedef struct {
    char name[32];                    // 药品名称
    medication_frequency_e frequency; // 服用频率(1-6)
    char method[32];                  // 服用方法
    char notes[64];                   // 注意事项
    bool need_to_take;                // 是否需要服用
    bool is_taken;                    // 是否已服用
} medication_info_t;
```

**智能时间调度**: 基于用户设定的服药时间和频率，自动计算执行分药任务。支持每日1-3次、隔日、每周1-2次等多种频率，可自定义时间段(早8点、中12点、晚6点)，提前5分钟语音提醒，超时后持续提醒。

**精准分药控制**: 通过6路PWM舵机实现精准药品分发。每个舵机对应一个药品仓位，支持0-180度精确控制，分药角度45度，休息角度90度。执行动作序列：移动到分药位置→等待分药→返回休息位置，具备角度限制、超时保护、异常检测等安全机制。

### 2.2 环境感知与交互系统

**多模态交互**:
- 语音交互：AI语音助手，支持自然语言指令和语音播报
- 按键交互：用户按键(P12)，支持单击分药、长按查询状态
- LED指示：系统LED(P01)，4种状态(关闭/常亮/慢闪/快闪)指示运行状态
- 移动端控制：通过涂鸦智能APP远程控制和监控

**智能环境适应**:
- 自动网络时间同步，确保分药时间准确
- 实时监控网络连接状态，离线时本地运行
- 低功耗设计，电量不足时自动节能
- 监控药品存储环境，异常时及时报警

### 2.3 数据管理与同步系统

**服药记录管理**: 采用Flash本地存储+云端同步的双重保障机制。64KB存储空间支持100条记录循环存储，通过DP118实时上传服药记录到云端，JSON格式包含时间戳、药品名称、服药状态，CRC32校验确保数据完整性。

```json
{
  "timestamp": 1703318400,
  "medication": "阿司匹林",
  "status": "completed",
  "slot": 1,
  "note": "按时服用"
}
```

**IoT数据通信**: 基于涂鸦IoT平台的可靠通信机制，支持低/普通/高/紧急4种优先级，最大3次重试，5秒发送超时，实时统计发送成功率和网络质量。

**关键DP点配置**:
- DP101-106: 药品01-06配置
- DP107: 错误日志上报
- DP108: 测试日志交互
- DP110: 舵机测试控制
- DP111-116: 1-6号门开关控制
- DP118: 服药记录缓存(重点)

## 3. 安全保障设计

**关键节点监控**: 对分药操作、用户交互、系统状态、硬件状态等关键节点进行全程监控和记录，确保操作可追溯。

**多重安全验证**:
- 时间窗口控制：只在指定时间窗口内允许分药
- 重复分药防护：同一时间段内防止重复分药
- 异常状态检测：检测舵机卡死、药品不足等异常
- 远程紧急停止：支持移动端紧急停止分药操作

**异常情况处理**: 完善的异常检测和处理机制，包括硬件异常(舵机故障、传感器失效)、网络异常(连接中断、传输失败)、用药异常(超时未服药、重复服药)、系统异常(内存不足、程序异常)等，按严重程度分级处理。

## 4. 典型使用场景

**日常用药场景**: 老年用户通过移动端APP配置6种药物信息，设置早中晚三个服药时间，系统自动在设定时间前5分钟语音提醒，用户按键确认后自动分发对应药物，服药记录自动上传云端供家属查看。

**紧急用药场景**: 用户突发不适时，通过语音指令"小涂，我需要紧急用药"，系统快速识别需求，语音确认药物类型后立即分药，绕过时间限制，标记为紧急用药并通知紧急联系人。

**远程监护场景**: 子女通过移动端实时查看药盒状态和药物余量，设置个性化提醒，接收超时未服药或异常情况报警，获取用药依从性报告和健康建议。

## 5. 技术规格与实现

**硬件规格**:
- 主控芯片：ESP32-S3 (双核240MHz)
- 存储容量：8MB Flash + 512KB SRAM
- 通信方式：Wi-Fi 802.11 b/g/n + 蓝牙5.0
- 舵机控制：6路PWM输出，50Hz频率
- 用户接口：1个LED指示灯 + 1个用户按键

**性能指标**:
- 分药精度：±2度角度控制精度
- 响应时间：<3秒分药响应时间
- 网络延迟：<1秒云端数据同步
- 存储容量：100条服药记录本地存储
- 电池续航：>30天待机时间

**模块化架构**: 采用标准化模块设计，已实现IoT通信管理、GPIO管理模块，正在开发服药日志管理模块，待实现时间管理、药品调度、舵机控制等模块。每个模块提供完整的测试用例和使用文档。

## 6. 质量保证与扩展规划

**测试体系**: 包含单元测试、集成测试、系统测试、压力测试的完整测试体系，每个模块提供`*_run_tests()`测试接口，覆盖功能验证、安全测试、用户体验测试。

**运维监控**: 实时监控硬件状态、网络状态、业务指标、用户行为，提供远程诊断、自检功能、标准化错误代码、安全恢复机制。

**未来扩展**: 计划集成AI健康分析、多设备联动、社区功能、个性化推荐等功能，技术上向边缘计算、5G连接、区块链、生物识别方向升级，生态上与医院、药店、保险、科研机构合作。

---

*文档版本: v1.0 | 最后更新: 2024-12-23 | 项目状态: 开发中*



---

*文档版本: v1.0*
*最后更新: 2024-12-23*
*项目状态: 开发中*
*下一版本计划: 完成服药日志管理模块后发布v1.1*
