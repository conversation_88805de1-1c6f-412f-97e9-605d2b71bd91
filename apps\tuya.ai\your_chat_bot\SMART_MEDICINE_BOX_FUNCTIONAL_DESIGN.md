# 智能药盒功能设计文档

## 1. 项目概述

### 1.1 项目背景
基于TuyaOpen平台开发的智能药盒系统，通过IoT技术、精准时间控制和智能提醒算法，为用户提供科学的用药管理和健康监护服务。系统集成了多传感器感知、云端数据同步、智能分药控制等核心功能。

### 1.2 设计目标
- **精准用药管理**: 确保用户按时按量服药，降低用药错误风险
- **智能健康监护**: 实时监控用药状态，提供健康数据分析
- **安全可靠运行**: 多重安全保障机制，确保系统稳定运行
- **便捷用户体验**: 简洁直观的交互界面，支持多种控制方式

### 1.3 技术架构
```
┌─────────────────────────────────────────────────────────┐
│                    云端管理平台                          │
│              (涂鸦IoT云 + 移动端APP)                     │
├─────────────────────────────────────────────────────────┤
│                    通信管理层                            │
│           (IoT通信管理 + DP数据同步)                     │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                            │
│    (用药管理 + 日志记录 + 调度控制 + 异常处理)           │
├─────────────────────────────────────────────────────────┤
│                    硬件控制层                            │
│        (GPIO管理 + 舵机控制 + 传感器接口)               │
├─────────────────────────────────────────────────────────┤
│                   TuyaOpen SDK                          │
│              (TAL API + TKL驱动层)                      │
└─────────────────────────────────────────────────────────┘
```

## 2. 核心功能设计

### 2.1 智能用药管理系统

#### 2.1.1 药品信息管理
**功能描述**: 支持6个独立药品仓位的信息管理，每个仓位可配置不同的药品信息。

**技术实现**:
- **数据存储**: 通过DP101-106分别管理6个药品的配置信息
- **信息格式**: `药品名称 频率枚举 方法枚举 注意事项 服用记录 需要服用标志 完成标志`
- **云端同步**: 实时同步到涂鸦IoT云平台，支持移动端查看和配置

**核心参数**:
```c
typedef struct {
    char name[32];              // 药品名称
    medication_frequency_e frequency;  // 服用频率(1-6)
    char method[32];            // 服用方法
    char notes[64];             // 注意事项
    char record[128];           // 服用记录
    bool need_to_take;          // 是否需要服用
    bool is_taken;              // 是否已服用
} medication_info_t;
```

#### 2.1.2 智能时间调度
**功能描述**: 基于用户设定的服药时间和频率，自动计算和执行分药任务。

**调度算法**:
- **频率支持**: 每日1-3次、隔日、每周1-2次
- **时间段管理**: 支持自定义时间段(如早8点、中12点、晚6点)
- **智能提醒**: 提前5分钟语音提醒，超时后持续提醒
- **自适应调整**: 根据用户服药习惯自动优化提醒时间

**时间段配置**:
```c
typedef struct {
    uint8_t start_hour;         // 开始小时
    uint8_t start_minute;       // 开始分钟
    uint8_t end_hour;           // 结束小时
    uint8_t end_minute;         // 结束分钟
    char label[16];             // 时间段标签
    bool dispensed_today;       // 今日是否已分药
} timeslot_t;
```

#### 2.1.3 精准分药控制
**功能描述**: 通过6路PWM舵机控制，实现精准的药品分发。

**控制机制**:
- **舵机配置**: 6个独立舵机对应6个药品仓位
- **角度控制**: 0-180度精确控制，分药角度45度，休息角度90度
- **动作序列**: 移动到分药位置 → 等待分药 → 返回休息位置
- **安全保护**: 角度限制、超时保护、异常检测

**硬件映射**:
```c
// 舵机PWM通道映射
static const TUYA_PWM_NUM_E servo_pwm_channels[6] = {
    TUYA_PWM_NUM_0,   // P06 - 药品仓位1
    TUYA_PWM_NUM_1,   // P07 - 药品仓位2
    TUYA_PWM_NUM_2,   // P08 - 药品仓位3
    TUYA_PWM_NUM_3,   // P09 - 药品仓位4
    TUYA_PWM_NUM_4,   // P10 - 药品仓位5
    TUYA_PWM_NUM_5,   // P11 - 药品仓位6
};
```

### 2.2 环境感知与交互系统

#### 2.2.1 多模态交互
**功能描述**: 支持语音、按键、移动端多种交互方式。

**交互方式**:
- **语音交互**: AI语音助手，支持语音指令和语音播报
- **按键交互**: 用户按键(P12)，支持单击和长按操作
- **LED指示**: 系统LED(P01)，4种状态指示系统运行状态
- **移动端控制**: 通过涂鸦智能APP远程控制和监控

**状态指示系统**:
```c
typedef enum {
    LED_STATE_OFF = 0,          // 系统休眠
    LED_STATE_ON,               // 正常运行
    LED_STATE_SLOW_BLINK,       // 待机/网络连接中
    LED_STATE_FAST_BLINK        // 警告/配置模式
} led_state_e;
```

#### 2.2.2 智能环境适应
**功能描述**: 根据环境变化自动调整系统行为。

**适应机制**:
- **时间同步**: 自动网络时间同步，确保分药时间准确
- **网络状态感知**: 实时监控网络连接状态，离线时本地运行
- **电源管理**: 低功耗设计，电量不足时自动进入节能模式
- **温湿度监控**: 监控药品存储环境，异常时及时报警

### 2.3 数据管理与同步系统

#### 2.3.1 服药记录管理
**功能描述**: 完整记录用户服药历史，支持本地存储和云端同步。

**存储机制**:
- **Flash本地存储**: 64KB存储空间，支持100条记录循环存储
- **云端同步**: 通过DP118实时上传服药记录到云端
- **数据格式**: JSON格式，包含时间戳、药品名称、服药状态
- **数据完整性**: CRC32校验，确保数据完整性

**记录格式**:
```json
{
  "timestamp": 1703318400,
  "medication": "阿司匹林",
  "status": "completed",
  "slot": 1,
  "note": "按时服用"
}
```

#### 2.3.2 IoT数据通信
**功能描述**: 基于涂鸦IoT平台的可靠数据通信机制。

**通信特性**:
- **多优先级队列**: 支持低/普通/高/紧急4种优先级
- **自动重试**: 最大3次重试，1秒重试间隔
- **超时控制**: 5秒发送超时，确保实时性
- **统计监控**: 发送成功率统计，网络质量监控

**DP点配置**:
```c
#define DP_MEDICINE_1           101     // 药品01配置
#define DP_MEDICINE_2           102     // 药品02配置
// ... DP103-106 药品03-06配置
#define DP_ERROR_LOG            107     // 错误日志
#define DP_TEST_LOG             108     // 测试日志
#define DP_INSTRUCTION          109     // 存储信息
#define DP_SERVO_TEST           110     // 舵机测试
#define DP_DOOR_1               111     // 1号门控制
// ... DP112-116 2-6号门控制
#define DP_TIMEOUT_REMIND       117     // 超时提醒
#define DP_MEDICATION_RECORD    118     // 服药记录缓存
```

## 3. 安全保障设计

### 3.1 关键节点监控
**功能描述**: 对系统关键操作节点进行全程监控和记录。

**监控节点**:
- **分药操作**: 记录每次分药的时间、药品、数量
- **用户交互**: 记录按键操作、语音指令、移动端操作
- **系统状态**: 记录系统启动、网络连接、异常情况
- **硬件状态**: 记录舵机运行、传感器数据、电源状态

**数据留档**:
```c
typedef struct {
    uint32_t timestamp;         // 时间戳
    char operation[32];         // 操作类型
    char details[128];          // 操作详情
    uint32_t result_code;       // 结果代码
} operation_log_t;
```

### 3.2 多重安全验证
**功能描述**: 通过多层安全机制确保用药安全。

**安全机制**:
- **时间窗口控制**: 只在指定时间窗口内允许分药
- **重复分药防护**: 同一时间段内防止重复分药
- **异常状态检测**: 检测舵机卡死、药品不足等异常
- **远程紧急停止**: 支持移动端紧急停止分药操作

### 3.3 异常情况处理
**功能描述**: 完善的异常检测和处理机制。

**异常类型**:
- **硬件异常**: 舵机故障、传感器失效、电源异常
- **网络异常**: 连接中断、数据传输失败、云端服务异常
- **用药异常**: 超时未服药、重复服药、药品不足
- **系统异常**: 内存不足、Flash损坏、程序异常

**处理策略**:
```c
typedef enum {
    EXCEPTION_LEVEL_INFO = 0,   // 信息级别
    EXCEPTION_LEVEL_WARN,       // 警告级别
    EXCEPTION_LEVEL_ERROR,      // 错误级别
    EXCEPTION_LEVEL_CRITICAL    // 严重级别
} exception_level_e;
```

## 4. 用户体验设计

### 4.1 智能提醒系统
- **多模态提醒**: 语音播报 + LED闪烁 + 移动端推送
- **渐进式提醒**: 轻柔提醒 → 标准提醒 → 强烈提醒
- **个性化设置**: 支持用户自定义提醒方式和强度

### 4.2 便捷操作界面
- **一键分药**: 按键单击即可手动分药
- **状态查询**: 长按按键查询系统状态
- **语音控制**: "小涂，帮我分药" 等自然语言指令

### 4.3 健康数据分析
- **服药依从性**: 统计按时服药率，生成依从性报告
- **用药趋势**: 分析用药规律，提供优化建议
- **健康提醒**: 根据用药情况提供健康建议

## 5. 技术规格

### 5.1 硬件规格
- **主控芯片**: ESP32-S3 (双核240MHz)
- **存储容量**: 8MB Flash + 512KB SRAM
- **通信方式**: Wi-Fi 802.11 b/g/n + 蓝牙5.0
- **舵机控制**: 6路PWM输出，50Hz频率
- **用户接口**: 1个LED指示灯 + 1个用户按键

### 5.2 软件规格
- **操作系统**: FreeRTOS
- **开发框架**: TuyaOpen SDK
- **编程语言**: C/C++
- **通信协议**: MQTT + HTTP/HTTPS
- **数据格式**: JSON + 二进制

### 5.3 性能指标
- **分药精度**: ±2度角度控制精度
- **响应时间**: <3秒分药响应时间
- **网络延迟**: <1秒云端数据同步
- **存储容量**: 100条服药记录本地存储
- **电池续航**: >30天待机时间

## 6. 典型使用场景

### 6.1 日常用药场景
**场景描述**: 老年用户需要每日三次服用不同药物

**操作流程**:
1. **初始配置**: 通过移动端APP配置6种药物信息
2. **时间设定**: 设置早8点、中12点、晚6点三个服药时间
3. **自动提醒**: 系统在设定时间前5分钟开始语音提醒
4. **确认分药**: 用户按下确认键，系统自动分发对应药物
5. **记录同步**: 服药记录自动上传云端，家属可实时查看

**技术支撑**:
- 药品调度管理模块实现精准时间控制
- GPIO管理模块处理用户按键交互
- 服药日志管理模块记录和同步数据

### 6.2 紧急用药场景
**场景描述**: 用户突发不适，需要紧急服药

**操作流程**:
1. **语音指令**: "小涂，我需要紧急用药"
2. **快速识别**: 系统识别紧急用药需求
3. **安全确认**: 通过语音确认药物类型和剂量
4. **立即分药**: 绕过时间限制，立即执行分药
5. **紧急记录**: 标记为紧急用药，通知紧急联系人

### 6.3 远程监护场景
**场景描述**: 子女远程监护父母用药情况

**监护功能**:
- **实时状态**: 查看药盒当前状态和药物余量
- **服药提醒**: 设置个性化提醒方式和频率
- **异常报警**: 超时未服药或异常情况立即通知
- **数据分析**: 生成用药依从性报告和健康建议

## 7. 系统集成与部署

### 7.1 模块化架构
**设计原则**: 采用模块化设计，便于功能扩展和维护

**核心模块**:
```c
// 已实现模块
iot_comm_manager        // IoT通信管理
gpio_manager           // GPIO硬件控制
medication_log_manager // 服药日志管理

// 待实现模块
time_manager           // 时间管理
thread_manager         // 线程管理
medication_scheduler   // 药品调度
servo_controller       // 舵机控制
```

### 7.2 配置管理
**配置文件**: 支持灵活的系统配置

```json
{
  "system": {
    "device_name": "智能药盒_001",
    "timezone": "Asia/Shanghai",
    "language": "zh-CN"
  },
  "medication": {
    "max_slots": 6,
    "reminder_advance": 300,
    "timeout_threshold": 1800
  },
  "hardware": {
    "servo_frequency": 50,
    "led_brightness": 80,
    "button_debounce": 50
  },
  "network": {
    "wifi_ssid": "SmartHome",
    "mqtt_server": "mqtt.tuya.com",
    "upload_interval": 60
  }
}
```

### 7.3 OTA升级支持
**功能特性**:
- **增量升级**: 支持差分升级，减少升级时间
- **断点续传**: 网络中断后可继续升级
- **回滚机制**: 升级失败自动回滚到稳定版本
- **安全验证**: 数字签名验证，确保固件安全

## 8. 质量保证与测试

### 8.1 功能测试
**测试覆盖**:
- **单元测试**: 每个模块提供完整的测试用例
- **集成测试**: 模块间协作功能测试
- **系统测试**: 端到端功能验证
- **压力测试**: 长时间运行稳定性测试

**测试工具**:
```c
// 模块测试接口
iot_comm_manager_run_tests();
gpio_manager_run_tests();
medication_log_manager_run_tests();
```

### 8.2 安全测试
**安全验证**:
- **数据加密**: 敏感数据传输加密验证
- **访问控制**: 权限管理和身份验证测试
- **异常处理**: 各种异常情况的处理验证
- **渗透测试**: 网络安全漏洞扫描

### 8.3 用户体验测试
**体验评估**:
- **易用性测试**: 不同年龄用户的操作体验
- **可访问性测试**: 视听障碍用户的使用体验
- **响应时间测试**: 系统响应速度评估
- **错误恢复测试**: 用户操作错误的恢复能力

## 9. 运维与监控

### 9.1 系统监控
**监控指标**:
- **硬件状态**: CPU使用率、内存占用、Flash磨损
- **网络状态**: 连接质量、数据传输成功率
- **业务指标**: 分药成功率、用药依从性
- **用户行为**: 操作频率、功能使用统计

### 9.2 日志管理
**日志分类**:
```c
typedef enum {
    LOG_LEVEL_DEBUG = 0,    // 调试信息
    LOG_LEVEL_INFO,         // 一般信息
    LOG_LEVEL_WARN,         // 警告信息
    LOG_LEVEL_ERROR,        // 错误信息
    LOG_LEVEL_FATAL         // 严重错误
} log_level_e;
```

### 9.3 故障诊断
**诊断工具**:
- **远程诊断**: 通过云端平台远程诊断设备状态
- **自检功能**: 系统启动时自动检测硬件状态
- **错误代码**: 标准化错误代码，便于问题定位
- **恢复模式**: 系统异常时的安全恢复机制

## 10. 未来扩展规划

### 10.1 功能扩展
**计划功能**:
- **AI健康分析**: 基于用药数据的健康状态分析
- **多设备联动**: 与血压计、血糖仪等设备联动
- **社区功能**: 用药经验分享和专家咨询
- **个性化推荐**: 基于用户习惯的个性化用药建议

### 10.2 技术升级
**技术路线**:
- **边缘计算**: 集成更多本地AI处理能力
- **5G连接**: 支持5G网络，提升数据传输速度
- **区块链**: 用药记录区块链存储，确保数据不可篡改
- **生物识别**: 指纹或人脸识别，提升安全性

### 10.3 生态建设
**生态合作**:
- **医院对接**: 与医院HIS系统对接，获取处方信息
- **药店合作**: 与连锁药店合作，提供药品配送服务
- **保险联动**: 与医疗保险合作，提供用药依从性数据
- **科研支持**: 为医学研究提供匿名化用药数据

---

*文档版本: v1.0*
*最后更新: 2024-12-23*
*项目状态: 开发中*
*下一版本计划: 完成服药日志管理模块后发布v1.1*
