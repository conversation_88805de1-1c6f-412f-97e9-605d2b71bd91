# C语言IoT传输层技术实现指南

## 4.1 传输层架构概述

本智能药盒项目采用多层次传输架构，基于TuyaOpen SDK实现设备与云端的可靠通信。传输层包含MQTT主通道、HTTP辅助通道、CoAP轻量通道和本地WebSocket服务，确保在不同网络环境下的通信稳定性和实时性。

### 4.1.1 传输层架构图

```
┌─────────────────────────────────────────────────────────┐
│                    涂鸦IoT云平台                         │
│              (MQTT Broker + HTTP API)                  │
├─────────────────────────────────────────────────────────┤
│                    TLS 1.2 安全层                       │
├─────────────────────────────────────────────────────────┤
│  MQTT 3.1.1  │  HTTP/HTTPS  │  CoAP/UDP  │  WebSocket  │
├─────────────────────────────────────────────────────────┤
│                    TCP/UDP 传输层                       │
├─────────────────────────────────────────────────────────┤
│                    LwIP 网络栈                          │
├─────────────────────────────────────────────────────────┤
│                    ESP32-S3 硬件                        │
└─────────────────────────────────────────────────────────┘
```

## 4.2 MQTT协议实现

### 4.2.1 MQTT连接建立

本项目通过TuyaOpen SDK的MQTT客户端与涂鸦IoT云平台建立连接。MQTT协议采用发布/订阅模式，设备作为客户端连接到云端MQTT Broker，实现双向数据传输。MQTT的轻量级特性和QoS质量保证机制为智能药盒的实时控制和数据上报提供了可靠保障。

```c
// MQTT连接配置
static TUYA_MQTT_BASE_CFG_T g_mqtt_cfg = {
    .host = "m1.tuyacn.com",           // 涂鸦MQTT服务器
    .port = 8883,                      // TLS加密端口
    .cacert = tuya_cacert_pem,         // CA根证书
    .timeout = 5000,                   // 连接超时5秒
    .keepalive = 120,                  // 心跳间隔2分钟
    .pub_buf_size = 1024,              // 发布缓冲区1KB
    .sub_buf_size = 1024,              // 订阅缓冲区1KB
};

// 建立MQTT连接
OPERATE_RET mqtt_connect_to_cloud(void) {
    // 生成客户端认证信息
    char client_id[64], username[64], password[128];
    generate_mqtt_credentials(client_id, username, password);
    
    // 设置连接参数
    g_mqtt_cfg.client_id = client_id;
    g_mqtt_cfg.username = username;
    g_mqtt_cfg.password = password;
    
    // 建立连接
    return tuya_mqtt_connect(&g_mqtt_cfg);
}
```

### 4.2.2 DP数据点通信

智能药盒通过涂鸦DP(Data Point)数据点模型与云端进行标准化通信。DP118用于上传服药记录，DP101-106管理药品配置，DP111-116控制舵机门开关。系统采用JSON格式封装DP数据，通过MQTT QoS 1级别确保重要数据的可靠传输。

```c
// DP118服药记录上传
OPERATE_RET upload_medication_record_dp118(const char* json_record) {
    TY_OBJ_DP_S dp_data = {
        .dpid = 118,                    // DP118服药记录缓存
        .type = PROP_STR,               // 字符串类型
        .value.dp_str = (char*)json_record,
        .time_stamp = 0                 // 实时型DP，框架自动填充时间
    };
    
    // 同步上报，QoS 1，确保送达
    OPERATE_RET ret = dev_report_dp_stat_sync(NULL, &dp_data, 1, 5);
    if (ret != OPRT_OK) {
        PR_ERR("DP118上传失败: %d", ret);
        // 失败时缓存到Flash，等待重试
        cache_failed_record_to_flash(json_record);
    }
    return ret;
}

// 舵机控制DP下发处理
void handle_servo_control_dp(TY_RECV_OBJ_DP_S *dp_recv) {
    for (int i = 0; i < dp_recv->dps_cnt; i++) {
        TY_OBJ_DP_S *dp = &dp_recv->dps[i];
        
        // 处理DP111-116舵机门控制
        if (dp->dpid >= 111 && dp->dpid <= 116) {
            int servo_id = dp->dpid - 111;  // 0-5对应6个舵机
            bool door_open = dp->value.dp_bool;
            
            PR_INFO("收到舵机%d控制指令: %s", servo_id, door_open ? "开门" : "关门");
            servo_controller_set_door_state(servo_id, door_open);
        }
    }
}
```

## 4.3 HTTP协议辅助通信

### 4.3.1 HTTP REST API

本项目中，设备配置信息、固件升级包、药品数据库等大容量数据通过HTTP协议与涂鸦云端API进行交互。HTTP协议的无状态特性和标准化接口为批量数据传输和系统集成提供了便利。

```c
// HTTP客户端配置
static http_client_config_t g_http_config = {
    .url = "https://openapi.tuyacn.com",
    .cert_pem = tuya_ca_cert,
    .timeout_ms = 10000,
    .buffer_size = 4096,
    .user_agent = "TuyaOpen-MedicationBox/1.0"
};

// 获取设备配置信息
OPERATE_RET fetch_device_config_via_http(char *config_buffer, size_t buffer_size) {
    esp_http_client_handle_t client = esp_http_client_init(&g_http_config);
    
    // 设置请求头
    esp_http_client_set_header(client, "Authorization", get_access_token());
    esp_http_client_set_header(client, "Content-Type", "application/json");
    
    // 发送GET请求
    esp_err_t err = esp_http_client_perform(client);
    if (err == ESP_OK) {
        int content_length = esp_http_client_get_content_length(client);
        if (content_length > 0 && content_length < buffer_size) {
            esp_http_client_read(client, config_buffer, content_length);
            config_buffer[content_length] = '\0';
        }
    }
    
    esp_http_client_cleanup(client);
    return (err == ESP_OK) ? OPRT_OK : OPRT_COM_ERROR;
}
```

### 4.3.2 OTA固件升级

智能药盒支持通过HTTP协议进行OTA(Over-The-Air)固件升级。系统定期检查云端是否有新版本固件，如有更新则下载并验证固件完整性，然后执行升级操作。升级过程采用双分区机制，确保升级失败时能够回滚到稳定版本。

```c
// OTA升级实现
OPERATE_RET perform_ota_upgrade(const char *firmware_url) {
    esp_http_client_config_t ota_config = {
        .url = firmware_url,
        .cert_pem = tuya_ca_cert,
        .timeout_ms = 30000,
        .keep_alive_enable = true,
    };
    
    esp_https_ota_config_t ota_cfg = {
        .http_config = &ota_config,
        .http_client_init_cb = NULL,
        .partial_http_download = true,
        .max_http_request_size = 4096,
    };
    
    PR_INFO("开始OTA升级，固件URL: %s", firmware_url);
    
    esp_err_t ret = esp_https_ota(&ota_cfg);
    if (ret == ESP_OK) {
        PR_INFO("OTA升级成功，重启设备");
        esp_restart();
    } else {
        PR_ERR("OTA升级失败: %d", ret);
        return OPRT_COM_ERROR;
    }
    
    return OPRT_OK;
}
```

## 4.4 CoAP轻量级协议

### 4.4.1 CoAP协议特性

对于电池供电的传感器节点或网络环境较差的场景，本项目集成了CoAP(Constrained Application Protocol)协议支持。CoAP基于UDP传输，具有更低的协议开销和功耗，适合间歇性通信的IoT设备。

```c
// CoAP客户端配置
static coap_client_config_t g_coap_config = {
    .server_ip = "coap.tuya.com",
    .server_port = 5683,
    .max_retransmit = 3,
    .ack_timeout_ms = 2000,
    .max_latency_ms = 100000,
};

// CoAP消息发送
OPERATE_RET send_coap_message(const char *uri_path, const char *payload) {
    coap_context_t *ctx = coap_new_context(NULL);
    coap_session_t *session = coap_new_client_session(ctx, NULL, &g_coap_config.server_addr, COAP_PROTO_UDP);
    
    coap_pdu_t *pdu = coap_pdu_init(COAP_MESSAGE_CON, COAP_REQUEST_POST, 0, coap_session_max_pdu_size(session));
    
    // 设置URI路径
    coap_add_option(pdu, COAP_OPTION_URI_PATH, strlen(uri_path), (const uint8_t *)uri_path);
    
    // 添加载荷数据
    coap_add_data(pdu, strlen(payload), (const uint8_t *)payload);
    
    // 发送消息
    coap_mid_t mid = coap_send(session, pdu);
    
    coap_session_release(session);
    coap_free_context(ctx);
    
    return (mid != COAP_INVALID_MID) ? OPRT_OK : OPRT_COM_ERROR;
}
```

## 4.5 本地WebSocket服务

### 4.5.1 WebSocket服务器实现

智能药盒内置WebSocket服务器，运行在端口8080，为本地Web管理界面提供实时数据通信。家属可通过浏览器访问设备IP地址，实时查看服药状态、设置提醒参数、查看历史记录等。WebSocket的全双工通信特性确保了数据的实时性。

```c
// WebSocket服务器配置
static httpd_config_t g_ws_config = {
    .task_priority = 5,
    .stack_size = 8192,
    .core_id = tskNO_AFFINITY,
    .server_port = 8080,
    .ctrl_port = 32768,
    .max_open_sockets = 4,
    .max_uri_handlers = 8,
    .max_resp_headers = 8,
    .backlog_conn = 5,
    .lru_purge_enable = true,
    .recv_wait_timeout = 5,
    .send_wait_timeout = 5,
};

// WebSocket消息处理
esp_err_t websocket_handler(httpd_req_t *req) {
    if (req->method == HTTP_GET) {
        PR_INFO("WebSocket握手请求");
        return ESP_OK;
    }
    
    httpd_ws_frame_t ws_pkt;
    uint8_t *buf = NULL;
    memset(&ws_pkt, 0, sizeof(httpd_ws_frame_t));
    
    // 接收WebSocket帧
    esp_err_t ret = httpd_ws_recv_frame(req, &ws_pkt, 0);
    if (ret != ESP_OK) {
        return ret;
    }
    
    if (ws_pkt.len) {
        buf = calloc(1, ws_pkt.len + 1);
        ws_pkt.payload = buf;
        ret = httpd_ws_recv_frame(req, &ws_pkt, ws_pkt.len);
        
        if (ret == ESP_OK) {
            // 处理接收到的消息
            process_websocket_message((char*)ws_pkt.payload);
            
            // 发送响应
            char response[] = "{\"status\":\"ok\",\"timestamp\":" STRINGIFY(CURRENT_TIMESTAMP) "}";
            httpd_ws_frame_t ws_resp = {
                .final = true,
                .fragmented = false,
                .type = HTTPD_WS_TYPE_TEXT,
                .payload = (uint8_t*)response,
                .len = strlen(response)
            };
            httpd_ws_send_frame(req, &ws_resp);
        }
        free(buf);
    }
    
    return ret;
}
```

### 4.5.2 实时数据推送

WebSocket服务器定期向连接的客户端推送设备状态数据，包括当前药品余量、下次服药时间、网络连接状态、电池电量等信息。推送频率可根据数据重要性动态调整，确保用户界面的实时性和流畅性。

```c
// 实时数据推送任务
void websocket_push_task(void *pvParameters) {
    char json_buffer[512];
    httpd_ws_frame_t ws_frame = {
        .final = true,
        .fragmented = false,
        .type = HTTPD_WS_TYPE_TEXT,
    };
    
    while (1) {
        // 收集设备状态数据
        medication_log_stats_t stats;
        medication_log_manager_get_stats(&stats);
        
        gpio_status_t gpio_status;
        gpio_manager_get_status(&gpio_status);
        
        // 格式化JSON数据
        snprintf(json_buffer, sizeof(json_buffer),
            "{"
            "\"timestamp\":%ld,"
            "\"medication_records\":%d,"
            "\"pending_uploads\":%d,"
            "\"led_state\":%d,"
            "\"button_state\":%d,"
            "\"network_quality\":%d,"
            "\"battery_level\":%d"
            "}",
            tal_time_get_posix(),
            stats.total_records,
            stats.pending_records,
            gpio_status.led_state,
            gpio_status.button_state,
            get_network_quality(),
            get_battery_level()
        );
        
        // 推送到所有连接的客户端
        ws_frame.payload = (uint8_t*)json_buffer;
        ws_frame.len = strlen(json_buffer);
        
        for (int i = 0; i < MAX_WS_CLIENTS; i++) {
            if (g_ws_clients[i].fd > 0) {
                httpd_ws_send_frame_async(g_httpd_handle, g_ws_clients[i].fd, &ws_frame);
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(5000));  // 5秒推送一次
    }
}
```

## 4.6 网络状态自适应

### 4.6.1 网络质量检测

系统实时监控网络连接质量，包括信号强度、延迟、丢包率等指标，根据网络状况动态调整通信策略。网络较差时降低数据上报频率、使用较低的QoS级别；网络良好时提高实时性、启用更多功能特性。

```c
// 网络质量评估
network_quality_t assess_network_quality(void) {
    wifi_ap_record_t ap_info;
    esp_wifi_sta_get_ap_info(&ap_info);
    
    int8_t rssi = ap_info.rssi;
    uint32_t ping_latency = measure_ping_latency("8.8.8.8");
    float packet_loss = measure_packet_loss_rate();
    
    // 综合评估网络质量
    if (rssi > -50 && ping_latency < 100 && packet_loss < 0.01) {
        return NETWORK_EXCELLENT;
    } else if (rssi > -70 && ping_latency < 500 && packet_loss < 0.05) {
        return NETWORK_GOOD;
    } else if (rssi > -80 && ping_latency < 1000 && packet_loss < 0.10) {
        return NETWORK_POOR;
    } else {
        return NETWORK_OFFLINE;
    }
}

// 网络自适应策略
void apply_network_adaptive_strategy(network_quality_t quality) {
    switch (quality) {
        case NETWORK_EXCELLENT:
            set_mqtt_keepalive(300);           // 5分钟心跳
            set_dp_report_interval(10);        // 10秒上报间隔
            enable_websocket_service(true);    // 启用WebSocket
            set_mqtt_qos_level(1);             // QoS 1
            break;
            
        case NETWORK_GOOD:
            set_mqtt_keepalive(120);           // 2分钟心跳
            set_dp_report_interval(30);        // 30秒上报间隔
            enable_websocket_service(true);    // 启用WebSocket
            set_mqtt_qos_level(1);             // QoS 1
            break;
            
        case NETWORK_POOR:
            set_mqtt_keepalive(60);            // 1分钟心跳
            set_dp_report_interval(60);        // 60秒上报间隔
            enable_websocket_service(false);   // 禁用WebSocket
            set_mqtt_qos_level(0);             // QoS 0
            break;
            
        case NETWORK_OFFLINE:
            enable_offline_mode(true);         // 启用离线模式
            cache_all_data_locally(true);      // 本地缓存所有数据
            break;
    }
}
```

---
*文档版本: v1.0*  
*最后更新: 2024-12-23*  
*适用项目: TuyaOpen智能药盒*
