# 智能药盒程序流程图指南

## 概述

本文档包含智能药盒项目中关键功能模块的程序执行流程图，展示了代码内部的详细执行逻辑、条件判断、错误处理和状态管理。这些流程图有助于理解程序的执行路径和调试问题。

## 流程图列表

### 1. MQTT连接建立程序流程图

**功能**: 展示设备与涂鸦IoT云平台建立MQTT连接的完整过程

**关键步骤**:
- 初始化MQTT配置结构体
- 生成设备认证信息(client_id, username, password)
- 配置TLS证书和加密端口8883
- 执行连接操作并处理重试逻辑
- 注册DP下发回调函数和订阅主题

**错误处理**:
- 连接失败时最多重试3次
- 记录详细的错误日志
- 返回相应的错误码

**代码对应**: `mqtt_connect_to_cloud()` 函数

### 2. DP118服药记录上传程序流程图

**功能**: 展示服药记录通过DP118上传到云端的详细过程

**关键步骤**:
- 参数有效性检查(JSON数据和长度验证)
- 构造TuyaOpen标准DP数据结构
- 调用同步上报接口，5秒超时等待
- 根据上报结果更新统计信息
- 失败时缓存到Flash等待重试

**数据结构**:
```c
TY_OBJ_DP_S dp_data = {
    .dpid = 118,                    // DP118服药记录缓存
    .type = PROP_STR,               // 字符串类型
    .value.dp_str = json_record,    // JSON格式数据
    .time_stamp = 0                 // 实时型DP
};
```

**代码对应**: `upload_medication_record_dp118()` 函数

### 3. 舵机控制DP下发处理程序流程图

**功能**: 展示接收到DP111-116舵机控制指令后的处理流程

**关键步骤**:
- 遍历接收到的DP数组
- 识别DP111-116范围内的舵机控制指令
- 计算对应的舵机ID(0-5)
- 检查舵机当前状态(空闲/忙碌)
- 执行PWM控制，转动到指定角度
- 角度传感器确认位置正确性
- 上报DP状态确认

**舵机控制逻辑**:
- door_open = true: 转动到45度分药位置
- door_open = false: 转动到90度休息位置
- 动作完成后等待2秒稳定
- 最多重试3次，失败时上报错误到DP107

**代码对应**: `handle_servo_control_dp()` 函数

### 4. 服药日志管理器主循环更新流程图

**功能**: 展示后台自动上传任务的执行逻辑

**关键步骤**:
- 检查初始化状态和自动上传开关
- 监控当前上传状态(IDLE/PENDING/UPLOADING)
- 60秒周期检查是否有待上传记录
- 执行批量上传操作
- 更新统计信息和状态标志

**状态管理**:
```c
typedef enum {
    UPLOAD_STATUS_IDLE = 0,         // 空闲状态
    UPLOAD_STATUS_PENDING,          // 等待上传
    UPLOAD_STATUS_UPLOADING,        // 正在上传
    UPLOAD_STATUS_SUCCESS,          // 上传成功
    UPLOAD_STATUS_FAILED            // 上传失败
} upload_status_e;
```

**代码对应**: `medication_log_manager_update()` 函数

### 5. 网络状态自适应程序流程图

**功能**: 展示根据网络质量动态调整系统参数的逻辑

**关键步骤**:
- 获取网络质量评估结果
- 根据质量等级执行不同的配置策略
- 调整MQTT心跳间隔、DP上报频率、QoS级别
- 控制WebSocket服务和监控功能的启用状态
- 离线模式下启用本地缓存机制

**网络质量等级**:
```c
typedef enum {
    NETWORK_OFFLINE = 0,    // 离线状态
    NETWORK_POOR,           // 网络较差
    NETWORK_GOOD,           // 网络良好
    NETWORK_EXCELLENT       // 网络优秀
} network_quality_t;
```

**自适应策略**:
- EXCELLENT: 心跳300秒，上报10秒，QoS 1，启用所有功能
- GOOD: 心跳120秒，上报30秒，QoS 1，启用基础功能
- POOR: 心跳60秒，上报60秒，QoS 0，禁用非关键功能
- OFFLINE: 离线模式，本地缓存，停止网络通信

**代码对应**: `apply_network_adaptive_strategy()` 函数

### 6. Flash存储写入程序流程图

**功能**: 展示服药记录写入Flash存储的详细过程

**关键步骤**:
- 参数有效性和初始化状态检查
- 创建flash_log_entry_t数据结构
- 设置魔数、时间戳、药品名称等字段
- 格式化日志内容字符串
- 计算CRC32校验值确保数据完整性
- 计算Flash写入地址(循环缓冲区)
- 执行Flash写入操作
- 更新头部信息(write_index, entry_count)

**Flash存储结构**:
```c
typedef struct {
    uint32_t magic;                     // 魔数验证
    uint32_t timestamp;                 // 时间戳
    char medication_name[32];           // 药品名称
    char log_entry[64];                 // 日志内容
    uint32_t crc;                       // CRC校验
    bool uploaded;                      // 是否已上传
} __attribute__((packed)) flash_log_entry_t;
```

**循环缓冲区逻辑**:
- 最大存储100条记录
- write_index循环递增: (index + 1) % MAX_ENTRIES
- 新记录覆盖最旧记录
- 头部信息实时更新

**代码对应**: `medication_log_manager_write_dispense()` 函数

## 流程图使用指南

### 1. 调试问题时的使用方法

**步骤1**: 确定问题所在的功能模块
**步骤2**: 查看对应的程序流程图
**步骤3**: 根据流程图中的判断条件设置断点
**步骤4**: 跟踪程序执行路径，定位问题节点

### 2. 代码审查时的参考

**检查点1**: 错误处理路径是否完整
**检查点2**: 状态转换逻辑是否正确
**检查点3**: 资源释放是否及时
**检查点4**: 边界条件是否考虑周全

### 3. 性能优化的参考

**优化点1**: 识别流程中的耗时操作
**优化点2**: 减少不必要的条件判断
**优化点3**: 优化循环和递归逻辑
**优化点4**: 合并相似的处理流程

### 4. 新功能开发的模板

**设计原则1**: 保持流程的清晰性和可读性
**设计原则2**: 完善的错误处理和状态管理
**设计原则3**: 合理的资源分配和释放
**设计原则4**: 充分的边界条件检查

## 流程图维护说明

### 更新时机
- 功能逻辑发生重大变更时
- 增加新的错误处理分支时
- 性能优化改变执行路径时
- 代码重构影响流程结构时

### 更新方法
1. 修改对应的Mermaid流程图定义
2. 更新流程图说明文档
3. 验证流程图与实际代码的一致性
4. 更新相关的测试用例

### 版本控制
- 流程图文件纳入Git版本控制
- 重大变更时创建版本标签
- 保留历史版本用于问题追溯

---
*文档版本: v1.0*  
*最后更新: 2024-12-23*  
*适用项目: TuyaOpen智能药盒*
